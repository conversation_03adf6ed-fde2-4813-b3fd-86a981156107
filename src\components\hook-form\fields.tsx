import { RHF<PERSON>ex<PERSON><PERSON>ield } from './rhf-text-field';
import { RHFSelectField } from './rhf-select-field';
import { RHFInstructionField } from './rhf-instruction-field';
import { RHFSwitch } from './rhf-switch';
import { RHFTextArea } from './rhf-textarea';
import RHFCode from './rhf-code';
import { DatePickerField } from './fields/date-picker-field';
import { RHFCheckbox, RHFMultiCheckbox } from './rhf-checkbox-field';
import { RHFFieldSlider } from './rhf-slider-field';
import RHFRadioGroup from './rhf-radio-group';

// ----------------------------------------------------------------------

export const Field = {
  Text: RHFTextField,
  Select: RHFSelectField,
  Instruction: R<PERSON><PERSON><PERSON>ructionField,
  RadioGroup: RHFRadioGroup,
  Switch: RHFSwitch,
  Area: RHFTextArea,
  Code: RHFCode,
  DatePicker: DatePickerField,
  Checkbox: RHFCheckbox,
  MultiCheckBox: RHFMultiCheckbox,
  FieldSlider: RHFFieldSlider,
};
