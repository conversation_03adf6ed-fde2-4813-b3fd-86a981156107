import { useTheme } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDebounce } from 'src/hooks/use-debounce';
import { useParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAuthToolsApi } from 'src/services/api/use-authtools-api';
import { useChatApi } from 'src/services/api/use-chat-api';
import { DataTemplatesTeam, useMyTeamTemplatesApi } from 'src/services/api/use-my-teams-api';
import { useTeamTeamplatessApi } from 'src/services/api/use-teams-api';
import { TemplateTool } from 'src/services/api/use-templates-api';
import { useToolConfigApi } from 'src/services/api/use-toolconfig-api';

const useAgentCloneView = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedToolForDropdown, setSelectedToolForDropdown] = useState<TemplateTool | null>(null);
  const { id, myTeamId } = useParams();

  const [toolsExpanded, setToolsExpanded] = useState(true);
  const [requirementsExpanded, setRequirementsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [specialRequest, setSpecialRequest] = useState('');

  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<TemplateTool | null>(null);
  const [selectToolId, setSelectedToolId] = useState<number | null>(null);
  const [expandedTool, setExpandedTool] = useState<number | null>(null);
  const [expandedTemplate, setExpandedTemplate] = useState<number | null>(null);
  const [selectedToolConfigs, setSelectedToolConfigs] = useState<
    { toolConfigId: number; toolId: number; configName?: string }[]
  >([]);
  const [oauthPopup, setOauthPopup] = useState<Window | null>(null);

  const [toolConfigSearchName, setToolConfigSearchName] = useState('');
  const depounceToolConfigSearchName = useDebounce(toolConfigSearchName, 2000);
  const theme = useTheme();
  const navigate = useNavigate();

  const { state } = useLocation();
  const agentName = state?.name || localStorage.getItem('templateTeamName');

  const code = localStorage.getItem('code');
  const stateTool = localStorage.getItem('state');

  const { useGetTeamTeamplates } = useTeamTeamplatessApi();
  const { useCreateMyTeamTemplates, useGetOneMyTeamTemplate, useUpdateMyTeamTemplates } =
    useMyTeamTemplatesApi();
  const { useCreateChat } = useChatApi();

  const { useGetAuthTools } = useAuthToolsApi();
  const { useGetToolConfigs } = useToolConfigApi();
  const { mutate: createMyTeam, isPending: isPendingMyTeam } = useCreateMyTeamTemplates();
  const { mutate: updateMyTeam, isPending: isPendingupdateMyTeam } = useUpdateMyTeamTemplates(
    myTeamId!
  );
  const { mutate: createChat, isPending: isPendingCreateChat } = useCreateChat();
  const { data: templatesResponseById, isLoading } = useGetTeamTeamplates(id as string);
  const { data: myTeamData } = useGetOneMyTeamTemplate(myTeamId!);
  if (id) {
    localStorage.setItem('typeOfConfigureation', 'team');
    localStorage.setItem('templateTeamId', id);
    localStorage.setItem('tempateTeamName', agentName);
  }
  const {
    data: dataAuthTools,
    isLoading: isLoadingToolsAuth,
    isError,
    isSuccess,
  } = useGetAuthTools(selectToolId !== null ? String(selectToolId) : '');

  const { data: dataConfigTools, isLoading: isLoadingConfigTools } = useGetToolConfigs({
    toolId: selectedTool?.toolId ? String(selectedTool.toolId) : '',
    name: depounceToolConfigSearchName,
  });

  useEffect(() => {
    if (isError || isSuccess) {
      setSelectedToolId(null);
      setSelectedTool(null);
      if (dataAuthTools?.url) {
        // Open OAuth URL in a popup window
        const popup = window.open(
          dataAuthTools.url,
          'oauth-popup',
          'width=600,height=700,scrollbars=yes,resizable=yes,status=yes,location=yes,toolbar=no,menubar=no'
        );
        setOauthPopup(popup);
        setExpandedTool(-1);
      }
    }
  }, [isError, isSuccess]);

  useEffect(() => {
    if (myTeamData?.teamTools) {
      const initialToolConfigs = myTeamData?.teamTools.map((tt) => ({
        toolConfigId: tt.toolConfigId, // this is the templateTool id
        toolId: tt.toolId, // the linked tool id
        configName: tt.toolConfig.name, // use the tool name
      }));

      setSelectedToolConfigs(initialToolConfigs);
    }
  }, [myTeamData]);

  // Listen for messages from the OAuth popup
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      if (event.origin !== window.location.origin) {
        return;
      }

      if (event.data.type === 'OAUTH_SUCCESS') {
        const { state, code } = event.data;
        if (state && code) {
          localStorage.setItem('state', state);
          localStorage.setItem('code', code);

          // Close the popup
          if (oauthPopup) {
            oauthPopup.close();
            setOauthPopup(null);
          }

          // Trigger any necessary updates in the main window
          // You can add additional logic here if needed
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [oauthPopup]);

  // Cleanup popup on component unmount
  useEffect(() => {
    return () => {
      if (oauthPopup && !oauthPopup.closed) {
        oauthPopup.close();
      }
    };
  }, [oauthPopup]);

  // Extract all tools from all templates in the team
  const allTools =
    templatesResponseById?.templatesInTeam?.flatMap(
      (templateInTeam) => templateInTeam.template.templateTools || []
    ) || [];

  const filteredTools = allTools.filter((tool) =>
    tool?.tool?.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group templates with their filtered tools
  const templatesWithFilteredTools =
    templatesResponseById?.templatesInTeam
      ?.map((templateInTeam) => {
        const filteredTemplateTools =
          templateInTeam.template.templateTools?.filter((tool) =>
            tool?.tool?.name.toLowerCase().includes(searchQuery.toLowerCase())
          ) || [];

        return {
          ...templateInTeam,
          template: {
            ...templateInTeam.template,
            templateTools: filteredTemplateTools,
          },
        };
      })
      .filter((templateInTeam) => templateInTeam.template.templateTools.length > 0) || [];

  const handleConfigureClick = (tool: TemplateTool) => {
    setSelectedToolId(tool?.toolId);
  };

  const handleDropdownClick = (event: React.MouseEvent<HTMLElement>, tool: TemplateTool) => {
    setSelectedTool(tool);
    setAnchorEl(event.currentTarget);
    setSelectedToolForDropdown(tool);
  };

  const handleDropdownClose = () => {
    setAnchorEl(null);
    setSelectedToolForDropdown(null);
  };

  const handleCloseConfigDialog = () => {
    setConfigDialogOpen(false);
    setSelectedTool(null);
  };

  const handleToolExpand = (toolId: number) => {
    setExpandedTool(expandedTool === toolId ? null : toolId);
    // Set the selected tool when expanding to fetch auth tools
    const tool = filteredTools.find((t) => t.id === toolId);
    if (tool) {
      setSelectedTool(tool);
    }
  };

  const handleTemplateExpand = (templateId: number) => {
    setExpandedTemplate(expandedTemplate === templateId ? null : templateId);
  };
  const handleToolConfigSelect = (toolConfigId: number, toolId: number, configName?: string) => {
    setSelectedToolConfigs((prev) => {
      // First remove any existing config for this toolId
      const filtered = prev.filter((config) => config.toolId !== toolId);

      // Then add the new config (if toolConfigId isn't -1)
      if (toolConfigId === -1) {
        return filtered; // -1 means deselect for this toolId
      }

      // Find the config name from dataConfigTools if not provided
      let finalConfigName = configName;
      if (!finalConfigName && dataConfigTools?.toolsConfigs) {
        const configTool = dataConfigTools.toolsConfigs.find(
          (tool: any) => tool.id === toolConfigId
        );
        finalConfigName = configTool?.name;
      }

      return [...filtered, { toolConfigId, toolId, configName: finalConfigName }];
    });
  };

  const handleSearchQueryName = (name: string) => {
    setToolConfigSearchName(name);
  };
  // Check if a tool has at least one selected configuration
  const isToolLinked = (toolId: number) => {
    return selectedToolConfigs.some((config) => config.toolId === toolId);
  };

  // Check if all tools are linked
  const areAllToolsLinked = () => {
    return filteredTools.every((tool) => isToolLinked(tool.toolId));
  };

  const handleContinueToTheChat = () => {
    const toolConfigIds = selectedToolConfigs?.map((item) => {
      return {
        toolConfigId: item?.toolConfigId,
      };
    });
    const data: DataTemplatesTeam = {
      templateTeamId: id && id?.length > 0 ? +id : 0,
      toolConfigIds,
      // specialRequest,
    };
    // if (data.specialRequest === '' || data.specialRequest == null) {
    //   delete data.specialRequest;
    // }
    if (myTeamId) {
      if (data?.templateTeamId && data?.templateTeamId > 0) delete data.templateTeamId;
      updateMyTeam(data as any, {
        onSuccess: (res) => {
          console.log('result in the onSuccess', res);
          const body = {
            title: 'new chat',
            userTeamId: res?.data?.id || '',
          };
          createChat(body, {
            onSuccess: (resChat) => {
              navigate(paths.dashboard.teams.chat(id!, res?.data?.id!, resChat?.data?.id!), {
                state: { name: templatesResponseById?.name },
              });
            },
          });
        },
      });
    } else {
      createMyTeam(data as any, {
        onSuccess: (res) => {
          const body = {
            title: 'new chat',
            userTeamId: res?.data?.id || '',
          };
          createChat(body, {
            onSuccess: (resChat) => {
              navigate(paths.dashboard.teams.chat(id!, res?.data?.id!, resChat?.data?.id!), {
                state: { name: templatesResponseById?.name },
              });
            },
          });
        },
      });
    }
  };

  return {
    templatesResponseById,
    theme,
    toolsExpanded,
    setToolsExpanded,
    searchQuery,
    setSearchQuery,
    filteredTools,
    templatesWithFilteredTools,
    // getStatusColor,
    // getStatusBgColor,
    setRequirementsExpanded,
    requirementsExpanded,
    setSpecialRequest,
    specialRequest,
    handleConfigureClick,
    selectedTool,
    configDialogOpen,
    handleCloseConfigDialog,
    isLoading,
    navigate,
    isPending: isPendingCreateChat || isPendingMyTeam || isPendingupdateMyTeam,
    handleContinueToTheChat,
    isLoadingToolsAuth,
    agentName,
    expandedTool,
    handleToolExpand,
    expandedTemplate,
    handleTemplateExpand,
    dataAuthTools,
    handleToolConfigSelect,
    selectedToolConfigs,
    dataConfigTools: dataConfigTools?.toolsConfigs,
    isLoadingConfigTools,
    stateTool,
    code,
    isToolLinked,
    areAllToolsLinked,

    depounceToolConfigSearchName,
    toolConfigSearchName,
    handleSearchQueryName,
    setToolConfigSearchName,
    setSelectedToolId,
    setSelectedTool,

    selectedToolForDropdown,
    setSelectedToolForDropdown,
    anchorEl,
    setAnchorEl,
    handleDropdownClose,
    handleDropdownClick,
  };
};

export default useAgentCloneView;
