import { useState, useMemo } from 'react';
import { <PERSON><PERSON>, Typo<PERSON>, Grid, Card, CardContent, Radio, FormControlLabel, RadioGroup } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { AgentFormValues, LLM_MODEL_OPTIONS } from '../config/agent-form-config';
import ServiceSearchBar from '../components/service-search-bar';

// ----------------------------------------------------------------------

interface ModelStepProps {
  // Add any specific props if needed
}

export function ModelStep(_props: ModelStepProps) {
  const { setValue, watch } = useFormContext<AgentFormValues>();
  const [searchQuery, setSearchQuery] = useState('');

  // Watch current selection
  const selectedModel = watch('model');

  // Filter models based on search
  const filteredModels = useMemo(() => {
    if (!searchQuery) return LLM_MODEL_OPTIONS;

    return LLM_MODEL_OPTIONS.filter(model =>
      model.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.provider.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleModelSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue('model', event.target.value as 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH');
  };

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Choose LLM Model
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search models..."
        />
        <RadioGroup
          value={selectedModel}
          onChange={handleModelSelect} 
        >
          <Grid container spacing={2}>
            {filteredModels.map((model) => {
              const isSelected = selectedModel === model.value;
              return (
                <Grid item xs={12} sm={12} key={model.value}>
                  <Card
                    variant="outlined"
                    sx={{
                      cursor: 'pointer',
                      backgroundColor: 'divider' ,
                      border: isSelected ? '2px solid' : '1px solid',
                      borderColor: isSelected ? 'primary.main' : 'divider',
                    }}
                    // Removed onClick from Card
                  >
                    <CardContent>
                      <FormControlLabel
                        value={model.value} 
                        control={<Radio checked={isSelected} />}
                        label={
                          <Stack spacing={1}>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Iconify icon={model.icon} />
                              <Typography variant="subtitle2">{model.label}</Typography>
                            </Stack>
                           
                          </Stack>
                        }
                        sx={{ width: '100%', margin: 0 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </RadioGroup>
      </Stack>
    </>
  );
}

export default ModelStep;