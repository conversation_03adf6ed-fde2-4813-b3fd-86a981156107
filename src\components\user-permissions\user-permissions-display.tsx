import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Chip,
  <PERSON><PERSON><PERSON>,
  IconButt<PERSON>,
  Stack,
  Divider,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { usePermissions } from 'src/auth/hooks/use-permissions';

// ----------------------------------------------------------------------

type UserPermissionsDisplayProps = {
  title?: string;
  showRoleInfo?: boolean;
  collapsible?: boolean;
  maxHeight?: number;
};

export function UserPermissionsDisplay({
  title = 'Your Permissions',
  showRoleInfo = true,
  collapsible = true,
  maxHeight = 300,
}: UserPermissionsDisplayProps) {
  const [expanded, setExpanded] = useState(!collapsible);
  const { userPermissions } = usePermissions();

  const handleToggle = () => {
    if (collapsible) {
      setExpanded(!expanded);
    }
  };

  // Group permissions by category for better organization
  const groupedPermissions = userPermissions.reduce((acc, permission) => {
    const [action, resource] = permission.split('-');
    const category = resource || 'general';
    
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(permission);
    return acc;
  }, {} as Record<string, string[]>);

  const getPermissionColor = (permission: string) => {
    if (permission.startsWith('create-')) return 'success';
    if (permission.startsWith('update-')) return 'info';
    if (permission.startsWith('delete-')) return 'error';
    if (permission.startsWith('get-')) return 'primary';
    if (permission.includes('admin')) return 'warning';
    return 'default';
  };

  return (
    <Card>
      <CardContent>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">
            {title} ({userPermissions.length})
          </Typography>
          {collapsible && (
            <IconButton onClick={handleToggle} size="small">
              <Iconify 
                icon={expanded ? 'eva:chevron-up-fill' : 'eva:chevron-down-fill'} 
              />
            </IconButton>
          )}
        </Stack>

        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            {userPermissions.length === 0 ? (
              <Typography color="text.secondary" variant="body2">
                No permissions assigned
              </Typography>
            ) : (
              <Box
                sx={{
                  maxHeight: collapsible ? maxHeight : 'none',
                  overflowY: 'auto',
                }}
              >
                {Object.entries(groupedPermissions).map(([category, permissions], index) => (
                  <Box key={category} sx={{ mb: index < Object.keys(groupedPermissions).length - 1 ? 2 : 0 }}>
                    <Typography 
                      variant="subtitle2" 
                      sx={{ 
                        mb: 1, 
                        textTransform: 'capitalize',
                        color: 'text.secondary' 
                      }}
                    >
                      {category.replace('-', ' ')}
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                      {permissions.map((permission) => (
                        <Chip
                          key={permission}
                          label={permission}
                          size="small"
                          color={getPermissionColor(permission) as any}
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      ))}
                    </Box>
                    {index < Object.keys(groupedPermissions).length - 1 && <Divider />}
                  </Box>
                ))}
              </Box>
            )}
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
}

// ----------------------------------------------------------------------

// Compact version for sidebars or small spaces
export function UserPermissionsCompact() {
  const { userPermissions, isAdmin } = usePermissions();

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Iconify 
        icon={isAdmin() ? 'eva:shield-fill' : 'eva:person-fill'} 
        sx={{ color: isAdmin() ? 'warning.main' : 'text.secondary' }}
      />
      <Typography variant="caption" color="text.secondary">
        {userPermissions.length} permissions
        {isAdmin() && ' (Admin)'}
      </Typography>
    </Box>
  );
}
