import { <PERSON>, Card, CardContent, Typography, Stack } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { usePermissions } from 'src/auth/hooks/use-permissions';
import { PermissionWrapper } from 'src/auth/guard/permission-based-guard';
import {
  PermissionButton,
  PermissionIconButton,
  PermissionFab,
} from 'src/components/permission-based';

// ----------------------------------------------------------------------

export default function PermissionDemo() {
  const {
    hasPermission,
    canCreate,
    canUpdate,
    canDelete,
    canAccessAgents,
    canAccessTemplates,
    canAccessTeams,
    canAccessTasks,
    isAdmin,
    userPermissions,
  } = usePermissions();

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Permission System Demo
      </Typography>

      {/* User Permissions Display */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Your Permissions ({userPermissions.length})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {userPermissions.map((permission) => (
              <Box
                key={permission}
                sx={{
                  px: 1,
                  py: 0.5,
                  bgcolor: 'primary.lighter',
                  color: 'primary.darker',
                  borderRadius: 1,
                  fontSize: '0.75rem',
                }}
              >
                {permission}
              </Box>
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Permission Checks */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Permission Checks
          </Typography>
          <Stack spacing={1}>
            <Typography>Can access agents: {canAccessAgents() ? '✅' : '❌'}</Typography>
            <Typography>Can access templates: {canAccessTemplates() ? '✅' : '❌'}</Typography>
            <Typography>Can access teams: {canAccessTeams() ? '✅' : '❌'}</Typography>
            <Typography>Can access tasks: {canAccessTasks() ? '✅' : '❌'}</Typography>
            <Typography>Is admin: {isAdmin() ? '✅' : '❌'}</Typography>
            <Typography>Can create agents: {canCreate('agent') ? '✅' : '❌'}</Typography>
            <Typography>Can update templates: {canUpdate('template') ? '✅' : '❌'}</Typography>
            <Typography>Can delete categories: {canDelete('category') ? '✅' : '❌'}</Typography>
          </Stack>
        </CardContent>
      </Card>

      {/* Permission-Based Buttons */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Permission-Based Buttons
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <PermissionButton
              permission="create-agent"
              variant="contained"
              startIcon={<Iconify icon="eva:plus-fill" />}
              fallback={
                <Typography variant="body2" color="text.disabled">
                  No permission to create agents
                </Typography>
              }
            >
              Create Agent
            </PermissionButton>

            <PermissionButton
              permission="update-template"
              variant="outlined"
              startIcon={<Iconify icon="eva:edit-fill" />}
              fallback={
                <Typography variant="body2" color="text.disabled">
                  No permission to edit templates
                </Typography>
              }
            >
              Edit Template
            </PermissionButton>

            <PermissionIconButton
              permission="delete-category"
              color="error"
              fallback={
                <Typography variant="body2" color="text.disabled">
                  No delete permission
                </Typography>
              }
            >
              <Iconify icon="eva:trash-2-fill" />
            </PermissionIconButton>

            <PermissionFab
              permission="create-task"
              color="primary"
              size="small"
              fallback={
                <Typography variant="body2" color="text.disabled">
                  No task creation permission
                </Typography>
              }
            >
              <Iconify icon="eva:plus-fill" />
            </PermissionFab>
          </Stack>
        </CardContent>
      </Card>

      {/* Permission Wrapper Examples */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Permission Wrapper Examples
          </Typography>
          <Stack spacing={2}>
            <PermissionWrapper permission="get-users">
              <Box sx={{ p: 2, bgcolor: 'success.lighter', borderRadius: 1 }}>
                <Typography color="success.darker">
                  🎉 You can see this because you have &apos;get-users&apos; permission!
                </Typography>
              </Box>
            </PermissionWrapper>

            <PermissionWrapper
              permission="login-admin"
              fallback={
                <Box sx={{ p: 2, bgcolor: 'warning.lighter', borderRadius: 1 }}>
                  <Typography color="warning.darker">
                    ⚠️ You need admin permissions to see the admin content.
                  </Typography>
                </Box>
              }
            >
              <Box sx={{ p: 2, bgcolor: 'info.lighter', borderRadius: 1 }}>
                <Typography color="info.darker">
                  🔐 Admin-only content: You have admin access!
                </Typography>
              </Box>
            </PermissionWrapper>

            <PermissionWrapper
              permission={['create-template', 'update-template']}
              options={{ requireAll: true }}
              fallback={
                <Typography variant="body2" color="text.disabled">
                  You need both create AND update template permissions to see this.
                </Typography>
              }
            >
              <Box sx={{ p: 2, bgcolor: 'primary.lighter', borderRadius: 1 }}>
                <Typography color="primary.darker">
                  📝 Template Management: You can create AND update templates!
                </Typography>
              </Box>
            </PermissionWrapper>

            <PermissionWrapper
              permission={['get-agents', 'get-templates', 'get-categories']}
              fallback={
                <Typography variant="body2" color="text.disabled">
                  You need at least one of: get-agents, get-templates, or get-categories
                  permissions.
                </Typography>
              }
            >
              <Box sx={{ p: 2, bgcolor: 'secondary.lighter', borderRadius: 1 }}>
                <Typography color="secondary.darker">
                  🤖 Content Management: You have access to at least one content type!
                </Typography>
              </Box>
            </PermissionWrapper>
          </Stack>
        </CardContent>
      </Card>

      {/* Custom Permission Logic */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Custom Permission Logic
          </Typography>
          <Stack spacing={2}>
            {hasPermission('get-tools') && (
              <Typography>✅ You can access the tools section</Typography>
            )}

            {hasPermission(['create-agent', 'create-template']) && (
              <Typography>✅ You can create content (agents or templates)</Typography>
            )}

            {hasPermission(['update-role', 'update-permission'], { requireAll: true }) && (
              <Typography>✅ You have full role management permissions</Typography>
            )}

            {!hasPermission('delete-user') && (
              <Typography color="warning.main">⚠️ You cannot delete users</Typography>
            )}
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
}
