import { Box, Breadcrumbs, Link, Typography } from '@mui/material';
import { Iconify } from 'src/components/iconify';

export interface FilePathBreadcrumbsProps {
  paths: { name: string; path: string }[];
  onPathClick?: (index: number) => void;
}

export default function FilePathBreadcrumbs({ paths, onPathClick }: FilePathBreadcrumbsProps) {
  return (
    <Box sx={{ mb: 2 }}>
      <Breadcrumbs>
        {paths.map((item, index) => {
          const isLast = index === paths.length - 1;

          return isLast ? (
            <Typography key={item.name} variant="body2" color="text.primary" fontWeight="600">
              {item.name}
            </Typography>
          ) : (
            <Link
              key={item.name}
              underline="hover"
              color="text.secondary"
              onClick={() => onPathClick?.(index)}
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
            >
              {index === 0 && paths.length > 1 && (
                <Iconify
                  icon="mdi:arrow-left"
                  sx={{ mr: 1, width: 20, height: 20, color: 'rgba(0, 0, 0, 1)' }}
                />
              )}
              {item.name}
            </Link>
          );
        })}
      </Breadcrumbs>
    </Box>
  );
}
