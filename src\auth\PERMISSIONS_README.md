# Permission-Based Access Control (RBAC) System

This system provides comprehensive role-based access control for your application, allowing you to show/hide UI elements and protect routes based on user permissions.

## Overview

The permission system is built around the user's permissions array returned from the `/me` API endpoint. It provides:

- **Permission-based navigation** - Menu items only show if user has required permissions
- **Route protection** - Pages are protected by permission guards
- **Component-level permissions** - Buttons, forms, and other UI elements can be conditionally rendered
- **Flexible permission checking** - Support for single permissions, multiple permissions (ANY/ALL logic)

## Core Components

### 1. `usePermissions` Hook

The main hook for checking permissions throughout your app.

```tsx
import { usePermissions } from 'src/auth/hooks/use-permissions';

function MyComponent() {
  const {
    hasPermission,
    canView,
    canCreate,
    canUpdate,
    canDelete,
    canAccessAgents,
    canAccessTemplates,
    // ... other helper methods
  } = usePermissions();

  // Check single permission
  if (hasPermission('create-agent')) {
    // User can create agents
  }

  // Check multiple permissions (ANY)
  if (hasPermission(['get-agents', 'get-templates'])) {
    // User has at least one of these permissions
  }

  // Check multiple permissions (ALL)
  if (hasPermission(['create-agent', 'update-agent'], { requireAll: true })) {
    // User has both permissions
  }
}
```

### 2. Permission Guards

#### `PermissionBasedGuard`
Protects entire components/pages based on permissions.

```tsx
import { PermissionBasedGuard } from 'src/auth/guard/permission-based-guard';

// Protect a page
<PermissionBasedGuard permission="get-agents">
  <AgentsPage />
</PermissionBasedGuard>

// Multiple permissions (ANY)
<PermissionBasedGuard permission={['get-agents', 'get-templates']}>
  <ContentPage />
</PermissionBasedGuard>

// Multiple permissions (ALL)
<PermissionBasedGuard 
  permission={['create-agent', 'update-agent']} 
  options={{ requireAll: true }}
>
  <AgentManagementPage />
</PermissionBasedGuard>
```

#### `PermissionWrapper`
Simple wrapper that hides content if user lacks permissions.

```tsx
import { PermissionWrapper } from 'src/auth/guard/permission-based-guard';

<PermissionWrapper permission="create-agent">
  <CreateAgentButton />
</PermissionWrapper>

// With fallback content
<PermissionWrapper 
  permission="admin-access"
  fallback={<Typography>Access denied</Typography>}
>
  <AdminPanel />
</PermissionWrapper>
```

### 3. Permission-Based Components

#### Buttons
```tsx
import { PermissionButton, PermissionIconButton, PermissionFab } from 'src/components/permission-based';

<PermissionButton
  permission="create-agent"
  variant="contained"
  fallback={<span>No permission</span>}
>
  Create Agent
</PermissionButton>

<PermissionIconButton permission="delete-agent" color="error">
  <DeleteIcon />
</PermissionIconButton>

<PermissionFab permission="create-task" color="primary">
  <AddIcon />
</PermissionFab>
```

## Navigation Integration

The navigation system automatically filters menu items based on permissions:

```tsx
// In config-nav-dashboard.tsx
const navItems = [
  {
    title: 'Agents',
    path: '/agents',
    permissions: ['get-agents'], // Only show if user has this permission
    children: [
      {
        title: 'Create Agent',
        path: '/agents/create',
        permissions: ['create-agent'],
      }
    ]
  }
];
```

## Route Protection

Routes are automatically protected using permission guards:

```tsx
// In dashboard routes
{
  path: '/agents',
  element: (
    <PermissionBasedGuard permission="get-agents">
      <AgentsPage />
    </PermissionBasedGuard>
  )
}
```

## Menu Actions

The `MaxHeightMenu` component automatically filters actions based on permissions:

```tsx
const menuOptions = [
  {
    label: 'Edit',
    onClick: handleEdit,
    permissions: ['update-agent'], // Only show if user has permission
    icon: 'eva:edit-fill'
  },
  {
    label: 'Delete',
    onClick: handleDelete,
    permissions: ['delete-agent'],
    icon: 'eva:trash-2-fill',
    color: 'error.main'
  }
];
```

## Permission Naming Convention

Permissions follow a consistent naming pattern:
- `get-{resource}` / `get-{resource}s` - View/read access
- `create-{resource}` - Create new items
- `update-{resource}` - Edit existing items
- `delete-{resource}` - Delete items
- `login-admin` - Admin access
- `toggle-{feature}` - Toggle specific features

## Helper Methods

The `usePermissions` hook provides convenient helper methods:

```tsx
const {
  canAccessAgents,     // Can view agents
  canAccessTemplates,  // Can view templates
  canAccessTeams,      // Can view teams
  canAccessTasks,      // Can view tasks
  canManageCategories, // Can manage categories
  isAdmin,             // Has admin access
  canView,             // canView('agent') checks 'get-agent'
  canCreate,           // canCreate('agent') checks 'create-agent'
  canUpdate,           // canUpdate('agent') checks 'update-agent'
  canDelete,           // canDelete('agent') checks 'delete-agent'
} = usePermissions();
```

## Best Practices

1. **Always check permissions** before showing UI elements that perform actions
2. **Use permission guards** for route protection
3. **Provide fallback content** for better UX when permissions are missing
4. **Use helper methods** for common permission patterns
5. **Keep permission names consistent** with the backend API
6. **Test with different user roles** to ensure proper access control

## Example Usage

See `src/sections/permission-examples/permission-demo.tsx` for comprehensive examples of all permission system features.
