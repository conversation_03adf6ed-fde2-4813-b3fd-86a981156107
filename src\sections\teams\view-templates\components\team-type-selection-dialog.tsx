import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  Typo<PERSON>,
  Box,
  Stack,
  DialogActions,
  Radio,
  FormControlLabel,
  RadioGroup,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { Form } from 'src/components/hook-form/form-provider';

// ----------------------------------------------------------------------

const teamTypeSchema = z.object({
  type: z.enum(['AUTO', 'MANUAL'], {
    required_error: 'Team type is required',
  }),
});

type TeamTypeFormValues = z.infer<typeof teamTypeSchema>;

interface TeamTypeSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (type: 'AUTO' | 'MANUAL') => void;
  initialValue?: string;
}

export default function TeamTypeSelectionDialog({
  open,
  onClose,
  onSubmit,
  initialValue = '',
}: TeamTypeSelectionDialogProps) {
  const { t } = useTranslation();

  // Form methods
  const methods = useForm<TeamTypeFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamTypeSchema),
    defaultValues: {
      type: 'AUTO' as const,
    },
  });

  const {
    handleSubmit,
    control,
    formState: { isSubmitting },
  } = methods;

  const handleFormSubmit = (data: TeamTypeFormValues) => {
    onSubmit(data.type);
  };

  const renderHead = (
    <Box sx={{ p: 3, pb: 0 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Typography variant="h6">Choose your teams type</Typography>
        <IconButton onClick={onClose}>
          <Iconify icon="mingcute:close-line" />
        </IconButton>
      </Stack>
    </Box>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <Box>
        {renderHead}

        <Form methods={methods} onSubmit={handleSubmit(handleFormSubmit)}>
          <DialogContent sx={{ p: 3, pt: 2 }}>
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <RadioGroup {...field} row>
                  <FormControlLabel
                    value="AUTO"
                    control={<Radio />}
                    label={
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        AUTO
                      </Typography>
                    }
                  />
                  <FormControlLabel
                    value="MANUAL"
                    control={<Radio />}
                    label={
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        MANUAL
                      </Typography>
                    }
                  />
                </RadioGroup>
              )}
            />
          </DialogContent>

          <DialogActions
            sx={{
              bgcolor: 'rgba(24, 0, 72, 0.04)',
              borderTop: '1px solid rgba(24, 0, 72, 0.04)',
              px: 3,
              py: 2,
            }}
          >
            <AppButton
              variant="outlined"
              color="inherit"
              label="Cancel"
              onClick={onClose}
              sx={{
                height: '40px',
                px: 4,
                width: '100px',
                borderRadius: 1,
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: 500,
              }}
            />
            <AppButton
              type="submit"
              variant="contained"
              color="primary"
              isLoading={isSubmitting}
              label="Continue"
              sx={{
                height: '40px',
                px: 4,
                width: '100px',
                borderRadius: 1,
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: 500,
              }}
            />
          </DialogActions>
        </Form>
      </Box>
    </Dialog>
  );
}
