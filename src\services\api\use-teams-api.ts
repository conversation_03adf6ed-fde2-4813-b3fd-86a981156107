import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for TeamTeamplatess
export const teamTeamplatesEndPoints = {
  list: '/templates-teams',
  details: '/templates-teams',
};

// Define the Category interface

// Define the TeamTeamplates data type based on the new API response
export interface TeamTeamplatesTool {
  id: number;
  TeamTeamplatesId: number;
  toolId: number;
  createdAt: string;
  tool: {
    id: number;
    icon: string;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  };
}

export type TeamInTemplatesType = {
  id: number;
  createdAt: string;
  template: {
    id: number;
    creatorId: number | null;
    name: string;
    description: string;
    categoryId: number;
    type: 'SINGLE' | 'MULTI' | string;
    model: string;
    status: 'ACTIVE' | 'INACTIVE' | string;
    createdAt: string;
    updatedAt: string;
    systemMessage: string;
    visibility: 'PUBLIC' | 'PRIVATE' | string;
    category: {
      id: number;
      name: string;
      description: string;
      icon: string;
      theme: string;
      createdAt: string;
      updatedAt: string;
    };
    templateTools: {
      id: number;
      templateId: number;
      toolId: number;
      createdAt: string;
      tool: {
        id: number;
        name: string;
        description: string;
        url: string | null;
        fields: unknown | null;
        icon: string;
        createdAt: string;
        updatedAt: string;
      };
    }[];
  };
};

interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  theme: string;
  createdAt: string;
  updatedAt: string;
}

export interface TeamTeamplatesType {
  id: number;
  createdAt: string;
  updatedAt: string;
  description: string;
  name: string;
  categoryId: number;
  type: 'AUTO' | 'MANUAL' | string;
  visibility: 'PUBLIC' | 'PRIVATE' | string;
  category: Category;
  model: string;
  edges?: TeamEdge[];
  templatesInTeam: TeamInTemplatesType[];
  status: 'ACTIVE' | 'INACTIVE' | string;
}

export interface TeamTeamplatessResponse {
  templatesTeams: TeamTeamplatesType[];
  total: number;
}

// Define the API response structure

// Define filter parameters interface
export interface TeamTeamplatesFilters {
  visibility?: 'PUBLIC' | 'PRIVATE';
  type?: string;
  category?: string;
  categoryId?: number;
  tools?: string;
  model?: string;
  status?: string;
  name?: string;
  take?: number;
  skip?: number;
}

export type TemplatesTeamsResponse = {
  templatesTeams: TeamTeamplatesType[];
  total: number;
};

export interface TeamEdge {
  id: string;
  source: number;
  dest: number;
  type: 'plain';
  label: string;
  description: string;
}
export interface SelectedTemplate {
  id: number;
  name: string;
  description?: string;
  model?: string;
  category: {
    icon: string;
  };
}

export interface TeamFormValues {
  name: string;
  description: string;
  type: 'AUTO' | 'MANUAL';
  categoryId: number;
  model?: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH';
  templatesIds: number[];
  selectedTemplates?: SelectedTemplate[]; // Store full template data for flow builder
  status: 'ACTIVE' | 'DISABLED';
  edges?: TeamEdge[];
  templatesTeams?: any;
}
export const TEAM_MODEL_OPTIONS = [
  { value: 'GPT_4O_MINI', label: 'GPT-4o Mini' },
  { value: 'GPT_4O', label: 'GPT-4o' },
  { value: 'CLAUDE_3_7_SONNET', label: 'Claude 3.7 Sonnet' },
  { value: 'GEMINI_2_0_FLASH', label: 'Gemini 2.0 Flash' },
  { value: 'GEMINI_1_5_FLASH', label: 'Gemini 1.5 Flash' },
] as const;

// Define the available type options as constants
export const TEAM_TYPE_OPTIONS = [
  { value: 'AUTO', label: 'Auto' },
  { value: 'MANUAL', label: 'Manual' },
];

// Define publish status request body
export interface PublishStatusRequest {
  status: 'APPROVED' | 'REJECTED';
}

// Create a hook to use the TeamTeamplatess API
export const useTeamTeamplatessApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all TeamTeamplatess with optional filters
  const useGetTeamTeamplatess = (filters?: TeamTeamplatesFilters) => {
    return apiServices.useGetListService<TemplatesTeamsResponse, TeamTeamplatesFilters>({
      url: teamTeamplatesEndPoints.list,
      params: filters,
    });
  };

  // Get a single TeamTeamplates by ID
  const useGetTeamTeamplates = (id: string) => {
    return apiServices.useGetItemService<TeamTeamplatesType>({
      url: teamTeamplatesEndPoints.details,
      id,
    });
  };

  // Create a new TeamTeamplates
  const useCreateTeamTeamplates = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<TeamTeamplatesType, any>({
      url: teamTeamplatesEndPoints.list,
      onSuccess,
    });
  };

  // Update a TeamTeamplates
  const useUpdateTeamTeamplates = (id: string | number, onSuccess?: () => void) => {
    return apiServices.usePatchService<TeamTeamplatesType>({
      url: teamTeamplatesEndPoints.details,
      id: id?.toLocaleString(),
      onSuccess,
    });
  };

  // Delete a TeamTeamplates
  const useDeleteTeamTeamplates = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any>({
      url: teamTeamplatesEndPoints.details,
      onSuccess,
    });
  };

  return {
    useGetTeamTeamplatess,
    useGetTeamTeamplates,
    useCreateTeamTeamplates,
    useUpdateTeamTeamplates,
    useDeleteTeamTeamplates,
  };
};
