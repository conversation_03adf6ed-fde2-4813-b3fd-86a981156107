import { useState, useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { paths } from 'src/routes/paths';
import { useTheme } from '@mui/material';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useTemplatesApi } from 'src/services/api/use-templates-api';
// import {
//   useTemplatesTeamsApi,
//   TEAM_MODEL_OPTIONS,
//   TEAM_TYPE_OPTIONS,
// } from 'src/services/api/use-templates-teams-api';
import { useNavigate } from 'react-router';
import { UserTeam } from 'src/services/api/use-my-teams-api';
import {
  TEAM_MODEL_OPTIONS,
  TEAM_TYPE_OPTIONS,
  TeamEdge,
  TeamFormValues,
  useTeamTeamplatessApi,
} from 'src/services/api/use-teams-api';
// import { TeamFormValues, EnhancedTemplateTeam, TeamEdge } from '../view/use-teams-view';

// Edge schema for flow builder
const edgeSchema = z.object({
  id: z.string(),
  source: z.number(),
  dest: z.number(),
  type: z.literal('plain'),
  label: z.string(),
  description: z.string(),
});

// Form validation schema
const teamSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.enum(['AUTO', 'MANUAL'], { required_error: 'Type is required' }),
  categoryId: z.number().min(1, 'Category is required'),
  model: z
    .enum(['GPT_4O_MINI', 'GPT_4O', 'CLAUDE_3_7_SONNET', 'GEMINI_2_0_FLASH', 'GEMINI_1_5_FLASH'])
    .optional(),

  templatesIds: z.array(z.number()).min(1, 'At least one template is required'),
  status: z.enum(['ACTIVE', 'DISABLED'], { required_error: 'Status is required' }),
  edges: z.array(edgeSchema).optional(),
});

// Hook props interface
interface UseTeamFormProps {
  team: UserTeam | null;
  initialTeamType?: 'AUTO' | 'MANUAL';
}

export function useTeamForm({ team, initialTeamType }: UseTeamFormProps) {
  const theme = useTheme();
  const [activeStep, setActiveStep] = useState(0);
  const navigate = useNavigate();

  // State for selected templates
  const [selectedTemplates, setSelectedTemplates] = useState<number[]>([]);

  // State for edges (flow builder)
  const [edges, setEdges] = useState<TeamEdge[]>([]);

  // API hooks
  const { useCreateTeamTeamplates, useUpdateTeamTeamplates } = useTeamTeamplatessApi();
  const { useGetCategories } = useCategoriesApi();
  const { useGetTemplates } = useTemplatesApi();

  const { mutate: createTeam, isPending: isCreating } = useCreateTeamTeamplates();
  const { mutate: updateTeam, isPending: isUpdating } = useUpdateTeamTeamplates(team?.id!);
  const { data: categoriesResponse } = useGetCategories();
  const { data: templatesResponse } = useGetTemplates();

  const categories = categoriesResponse?.categories || [];
  const templates = templatesResponse?.templates || [];
  const isLoading = isCreating || isUpdating;
  const isEditing = Boolean(team);

  // Form setup
  const methods = useForm<TeamFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamSchema),
    defaultValues: {
      name: '',
      description: '',
      type: initialTeamType || ('AUTO' as const),
      categoryId: 0,
      model: 'GPT_4O_MINI' as const,
      templatesIds: [],
      status: 'ACTIVE',
      edges: [],
    },
  });

  // Get current form values to determine team type
  const currentFormValues = methods.watch();
  const isManualType = currentFormValues.type === 'MANUAL';

  // Define steps similar to agent form
  const steps = useMemo(() => {
    const baseSteps = [
      {
        label: 'Team Info',
        fields: ['name', 'description', 'status'],
      },
      {
        label: 'Category',
        fields: ['categoryId'],
      },
      {
        label: 'Agent',
        fields: ['templatesIds'],
      },
    ];

    // Add Flow Builder step for MANUAL type
    if (isManualType) {
      baseSteps.push({
        label: 'Flow Builder',
        fields: ['edges'],
      });
    }

    // Add Model step at the end
    if (!isManualType) {
      baseSteps.push({
        label: 'Model',
        fields: ['model'],
      });
    }

    return baseSteps;
  }, [isManualType]);

  const {
    handleSubmit,
    trigger,
    setValue,
    reset,
    formState: { isSubmitting },
  } = methods;

  // Reset form when team changes
  useEffect(() => {
    if (team) {
      // Pre-fill form with team data for editing
      const templateData = team.templateData;

      // Convert EdgesType to TeamEdge format
      const teamEdges: TeamEdge[] = (team.edges || []).map((edge) => ({
        id: edge.id,
        source: edge.source,
        dest: edge.dest,
        type: 'plain' as const,
        label: edge.label,
        description: edge.description,
      }));

      reset({
        name: team.name || templateData?.name || '',
        description: team.description || templateData?.description || '',
        type: team.type as 'AUTO' | 'MANUAL',
        categoryId: team.categoryId || templateData?.categoryId || 0,
        model: team.model || templateData?.model || 'GPT_4O_MINI',
        templatesIds: team.templatesInTeam?.map((t) => t.template.id) || [],
        status: team.status, // Preserve the existing status
        edges: teamEdges,
        selectedTemplates: team.templatesInTeam?.map((t) => t.template) || [],
      });
      setSelectedTemplates(team.templatesInTeam?.map((t) => t.template.id) || []);
      setEdges(teamEdges);
    } else {
      // Reset to default values for creating new team
      reset({
        name: '',
        description: '',
        type: initialTeamType || ('AUTO' as const),
        categoryId: 0,
        model: 'GPT_4O_MINI' as const,
        templatesIds: [],
        status: 'ACTIVE', // Default status for new team
        edges: [],
        selectedTemplates: [],
      });
      setSelectedTemplates([]);
      setEdges([]);
    }
  }, [team, reset]);

  // Navigation handlers
  const handleNext = useCallback(async () => {
    const currentStep = steps[activeStep];
    if (!currentStep) return;

    // Validate current step fields
    const isStepValid = await trigger(currentStep.fields as any);

    if (isStepValid) {
      if (activeStep < steps.length - 1) {
        setActiveStep((prev) => prev + 1);
      }
    }
  }, [activeStep, trigger, steps]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  }, [activeStep]);

  // Handle template selection
  const handleTemplateToggle = useCallback(
    (templateId: number) => {
      setSelectedTemplates((prev) => {
        const newSelection = prev.includes(templateId)
          ? prev.filter((id) => id !== templateId)
          : [...prev, templateId];

        setValue('templatesIds', newSelection);
        return newSelection;
      });
    },
    [setValue]
  );

  // Handle edge management for flow builder
  const handleEdgesChange = useCallback(
    (newEdges: TeamEdge[]) => {
      setEdges(newEdges);
      setValue('edges', newEdges);
    },
    [setValue]
  );

  // Form submission
  const onFormSubmit = handleSubmit(async (data: TeamFormValues) => {
    try {
      if (isEditing) {
        // Update existing team
        await new Promise((resolve, reject) => {
          if (isManualType && data && data.model) {
            delete data?.model;
          }
          updateTeam(data, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      } else {
        // Create new team
        await new Promise((resolve, reject) => {
          if (isManualType && data && data.model) {
            delete data?.model;
          }
          createTeam(data, {
            onSuccess: resolve,
            onError: reject,
          });
        });
      }

      // Navigate back to teams list
      navigate(paths.dashboard.teams.root);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  });

  return {
    // Form state
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    theme,

    // Navigation
    handleNext,
    handleBack,

    // Form submission
    onFormSubmit,

    // Data and filtering
    categories,
    templates,
    selectedTemplates,

    // Selection handlers
    handleTemplateToggle,
    handleEdgesChange,

    // Flow builder state
    edges,
    isManualType,

    // Constants and options
    steps,
    totalSteps: steps.length,
    isLastStep: activeStep === steps.length - 1,
    isFirstStep: activeStep === 0,
    TEAM_TYPE_OPTIONS,
    TEAM_MODEL_OPTIONS,
  };
}
