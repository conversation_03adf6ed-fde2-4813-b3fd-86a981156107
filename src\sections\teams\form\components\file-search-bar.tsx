import { InputAdornment, TextField, SxProps, Theme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';

interface FileSearchBarProps {
  query: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  sx?: SxProps<Theme>;
}

export default function FileSearchBar({
  query,
  onChange,
  placeholder,
  sx,
}: FileSearchBarProps) {
  const { t } = useTranslation();
  const defaultPlaceholder = t('components.common.searchFiles');
  return (
    <TextField
      fullWidth
      value={query}
      onChange={onChange}
      placeholder={placeholder || defaultPlaceholder}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
          </InputAdornment>
        ),
      }}
      sx={{
        mb: 3,
        '& .MuiOutlinedInput-root': {
          borderRadius: 1,
        },
        ...sx,
      }}
    />
  );
}
