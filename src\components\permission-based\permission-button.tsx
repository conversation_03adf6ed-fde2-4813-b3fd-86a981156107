import { Button, IconButton, Fab } from '@mui/material';
import type { ButtonProps, IconButtonProps, FabProps } from '@mui/material';

import { usePermissions, type PermissionCheckOptions } from 'src/auth/hooks/use-permissions';

// ----------------------------------------------------------------------

type PermissionButtonProps = ButtonProps & {
  permission: string | string[];
  options?: PermissionCheckOptions;
  fallback?: React.ReactNode;
};

export function PermissionButton({
  permission,
  options = { requireAll: false },
  fallback = null,
  children,
  ...other
}: PermissionButtonProps) {
  const { hasPermission } = usePermissions();

  const hasAccess = hasPermission(permission, options);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <Button {...other}>{children}</Button>;
}

// ----------------------------------------------------------------------

type PermissionIconButtonProps = IconButtonProps & {
  permission: string | string[];
  options?: PermissionCheckOptions;
  fallback?: React.ReactNode;
};

export function PermissionIconButton({
  permission,
  options = { requireAll: false },
  fallback = null,
  children,
  ...other
}: PermissionIconButtonProps) {
  const { hasPermission } = usePermissions();

  const hasAccess = hasPermission(permission, options);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <IconButton {...other}>{children}</IconButton>;
}

// ----------------------------------------------------------------------

type PermissionFabProps = FabProps & {
  permission: string | string[];
  options?: PermissionCheckOptions;
  fallback?: React.ReactNode;
};

export function PermissionFab({
  permission,
  options = { requireAll: false },
  fallback = null,
  children,
  ...other
}: PermissionFabProps) {
  const { hasPermission } = usePermissions();

  const hasAccess = hasPermission(permission, options);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <Fab {...other}>{children}</Fab>;
}
