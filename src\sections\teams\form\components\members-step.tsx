import { useState } from 'react';
import {
  <PERSON>,
  Stack,
  Typography,
  Checkbox,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemButton,
  ListItemIcon,
  Divider,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import FileSearchBar from './file-search-bar';

// Role colors mapping using theme tokens
const ROLE_COLORS: Record<
  string,
  'error' | 'info' | 'warning' | 'success' | 'secondary' | 'primary' | 'default'
> = {
  'Team Lead': 'error',
  Developer: 'info',
  Designer: 'warning',
  Marketing: 'success',
  Support: 'secondary',
  Research: 'primary',
  'Social Media': 'error',
  Content: 'info',
  Analytics: 'warning',
};

// Mock data for team members with additional details
const MOCK_MEMBERS = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>, the UX team lead',
    avatarUrl: '/assets/images/avatar/avatar_1.jpg',
    role: 'Team Lead',
    description: 'Starts a comprehensive UX research based on a detailed analysis which .',
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>, the UI designer',
    avatarUrl: '/assets/images/avatar/avatar_2.jpg',
    role: 'Developer',
    description: 'Frontend development and UI implementation',
  },
  {
    id: '3',
    name: 'Naofumi, the UX team lead',
    avatarUrl: '/assets/images/avatar/avatar_3.jpg',
    role: 'Designer',
    description: 'UI/UX design and visual assets creation',
  },
  {
    id: '4',
    name: 'Naofumi, the UX team lead',
    avatarUrl: '/assets/images/avatar/avatar_4.jpg',
    role: 'Marketing',
    description: 'Product marketing and customer outreach',
  },
  {
    id: '5',
    name: 'David Brown',
    avatarUrl: '/assets/images/avatar/avatar_5.jpg',
    role: 'Support',
    description: 'Customer support and issue resolution',
  },
  {
    id: '6',
    name: 'Sarah Johnson',
    avatarUrl: '/assets/images/avatar/avatar_6.jpg',
    role: 'Research',
    description: 'Market research and competitive analysis',
  },
  {
    id: '7',
    name: 'Alex Rodriguez',
    avatarUrl: '/assets/images/avatar/avatar_7.jpg',
    role: 'Social Media',
    description: 'Social media management and content creation',
  },
  {
    id: '8',
    name: 'Lisa Chen',
    avatarUrl: '/assets/images/avatar/avatar_8.jpg',
    role: 'Content',
    description: 'Content writing and documentation',
  },
  {
    id: '9',
    name: 'Kevin Park',
    avatarUrl: '/assets/images/avatar/avatar_9.jpg',
    role: 'Analytics',
    description: 'Data analysis and performance tracking',
  },
];

interface MembersStepProps {
  selectedMembers: string[];
  onMemberChange: (members: string[]) => void;
}

export default function MembersStep({ selectedMembers = [], onMemberChange }: MembersStepProps) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleToggleMember = (memberId: string) => {
    const newSelectedMembers = selectedMembers.includes(memberId)
      ? selectedMembers.filter((id) => id !== memberId)
      : [...selectedMembers, memberId];

    onMemberChange(newSelectedMembers);
  };

  // Filter members based on search query
  const filteredMembers = MOCK_MEMBERS.filter(
    (member) =>
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Stack spacing={3}>


      <Box
        sx={{
            backgroundColor: 'background.neutral',
          border: '1px solid',
          borderColor: 'divider',
          p: 3,
          borderRadius: 2,
          maxHeight: '60vh',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Typography variant="h6" sx={{ mb: 2 }}>
          {t('teams.resources.chooseTeamMembers')}
        </Typography>
        <FileSearchBar
        query={searchQuery}
        onChange={handleSearchChange}
        placeholder={t('teams.resources.searchMembers')}
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: '48px',
            border: '1px solid',
            borderColor: 'divider',
            background: 'background.paper',
          },
        }}
      />
        <Box
          sx={{
            overflow: 'auto',
            flex: 1,
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            msOverflowStyle: 'none',
            scrollbarWidth: 'none',
          }}
        >
          <List sx={{ width: '100%', bgcolor: 'background.neutral', p: 0 }}>
            {filteredMembers.map((member, index) => (
              <Box key={member.id}>
                {index > 0 && <Divider component="li" />}
                <ListItem disablePadding sx={{ py: 1 }}>
                  <ListItemButton
                    onClick={() => handleToggleMember(member.id)}
                    dense
                    sx={{
                      borderRadius: 1,
                      py: 1.5,
                      px: 2,
                    }}
                  >
                    <ListItemIcon sx={{ maxWidth: 40 }}>
                      <Checkbox
                        edge="start"
                        checked={selectedMembers.includes(member.id)}
                        tabIndex={-1}
                        disableRipple
                        sx={{
                          color: 'text.primary',
                          '&.Mui-checked': {
                            color: 'success.main',
                          },
                          '& .MuiSvgIcon-root': { fontSize: 35 },
                        }}
                      />
                    </ListItemIcon>
                    <ListItemAvatar sx={{ minWidth: 56 }}>
                      <Avatar
                        alt={member.name}
                        src={member.avatarUrl}
                        sx={{ width: 48, height: 48 }}
                      />
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {member.name}
                          </Typography>
                          <Chip
                            label={member.role}
                            color={ROLE_COLORS[member.role] || 'default'}
                            size="small"
                            sx={{
                              fontWeight: 600,
                              fontSize: '0.75rem',
                              height: 24,
                            }}
                          />
                        </Stack>
                      }
                      secondary={
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: '0.875rem' }}
                        >
                          {member.description}
                        </Typography>
                      }
                      sx={{ my: 0 }}
                    />
                  </ListItemButton>
                </ListItem>
              </Box>
            ))}
          </List>
        </Box>
      </Box>
    </Stack>
  );
}
