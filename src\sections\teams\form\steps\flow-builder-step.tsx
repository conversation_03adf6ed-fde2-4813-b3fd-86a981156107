import React, { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  <PERSON>,
  Stack,
  Typography,
  <PERSON><PERSON>,
  Drawer,
  IconButton,
  Paper,
  Chip,
  Divider,
  Grid,
  Card,
  CardContent,
  alpha,
} from '@mui/material';
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  BackgroundVariant,
  Handle,
  Position,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { TeamFormValues, TeamEdge, SelectedTemplate } from '../../view/use-teams-view';

// ----------------------------------------------------------------------

// Custom node component for agents
const AgentNode = ({ data }: { data: any }) => {
  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Top}
        style={{
          background: '#7D40D9',
          width: 12,
          height: 12,
          border: '2px solid white',
        }}
      />

      <Paper
        elevation={3}
        sx={{
          p: 2,
          minWidth: 220,
          maxWidth: 280,
          borderRadius: 3,
          border: '2px solid',
          borderColor: '#7D40D9',
          bgcolor: 'background.paper',
          position: 'relative',
          '&:hover': {
            boxShadow: '0 8px 25px rgba(125, 64, 217, 0.15)',
          },
        }}
      >
        <Stack spacing={1.5}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                bgcolor: '#7D40D9',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '14px',
              }}
            >
              A
            </Box>
            <Typography variant="subtitle2" fontWeight={600} noWrap>
              {data.label}
            </Typography>
          </Stack>

          {data.description && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                lineHeight: 1.3,
              }}
            >
              {data.description}
            </Typography>
          )}

          {data.model && (
            <Chip
              label={data.model}
              size="small"
              color="primary"
              variant="outlined"
              sx={{ alignSelf: 'flex-start' }}
            />
          )}
        </Stack>
      </Paper>

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        style={{
          background: '#7D40D9',
          width: 12,
          height: 12,
          border: '2px solid white',
        }}
      />
    </>
  );
};

import { BaseEdge, EdgeLabelRenderer, getBezierPath } from '@xyflow/react';

const RemovableEdge = ({ id, sourceX, sourceY, targetX, targetY, markerEnd, style, data }: any) => {
  const [edgePath, labelX, labelY] = getBezierPath({ sourceX, sourceY, targetX, targetY });

  return (
    <>
      <BaseEdge id={id} path={edgePath} markerEnd={markerEnd} style={style} />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            pointerEvents: 'all',
          }}
          className="nodrag nopan"
        >
          <button
            style={{
              background: 'white',
              border: '1px solid #7D40D9',
              borderRadius: '50%',
              cursor: 'pointer',
              width: 20,
              height: 20,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: 12,
              color: '#7D40D9',
            }}
            onClick={() => data?.onRemove(id)}
          >
            ✕
          </button>
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

const edgeTypes = {
  removable: RemovableEdge,
};

// Node types
const nodeTypes = {
  agentNode: AgentNode,
};

export function FlowBuilderStep() {
  const { watch, setValue } = useFormContext<TeamFormValues>();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const initialNodes: Node[] = [];
  const initialEdges: Edge[] = [];

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // Pagination state for selected agents
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 5;

  // Get selected agents data directly from form
  const selectedTemplates = watch('selectedTemplates') || [];
  const formEdges = watch('edges') || [];

  // Initialize edges and nodes from form data when editing
  useEffect(() => {
    if (formEdges.length > 0 && edges.length === 0) {
      // Convert TeamEdge to ReactFlow Edge format
      const reactFlowEdges: Edge[] = formEdges.map((edge) => ({
        id: edge.id,
        source: edge.source.toString(),
        target: edge.dest.toString(),
        type: 'removable',
        animated: true,
        style: { stroke: '#7D40D9', strokeWidth: 2 },
        markerEnd: { type: 'arrowclosed', color: '#7D40D9' },
        data: {
          onRemove: (id: string) => {
            setEdges((eds) => eds.filter((e) => e.id !== id));

            // sync with form
            const formEdges: TeamEdge[] = edges
              .filter((e) => e.id !== id)
              .map((edge) => ({
                id: edge.id,
                source: parseInt(edge.source, 10),
                dest: parseInt(edge.target, 10),
                type: 'plain' as const,
                label: `edge-${edge.source}-${edge.target}`,
                description: `edge-${edge.source}-${edge.target}`,
              }));

            setValue('edges', formEdges);
          },
        },
      }));
      setEdges(reactFlowEdges);

      // Create nodes for agents that are already connected
      const connectedAgentIds = new Set<string>();
      formEdges.forEach((edge) => {
        connectedAgentIds.add(edge.source.toString());
        connectedAgentIds.add(edge.dest.toString());
      });

      const existingNodes: Node[] = Array.from(connectedAgentIds).map((agentId, index) => {
        const template = selectedTemplates.find((t) => t.id.toString() === agentId);
        return {
          id: agentId,
          type: 'agentNode',
          position: { x: 100 + index * 200, y: 100 + (index % 2) * 150 },
          data: {
            name: template?.name || `Agent ${agentId}`,
            description: template?.description || '',
            icon: template?.category?.icon || 'mdi:robot',
          },
        };
      });

      if (existingNodes.length > 0) {
        setNodes(existingNodes);
      }
    }
  }, [formEdges, edges.length, selectedTemplates, setEdges, setNodes]);

  // Sync edges changes with form
  useEffect(() => {
    const teamEdges: TeamEdge[] = edges.map((edge) => ({
      id: edge.id || '',
      source: parseInt(edge.source || '0', 10),
      dest: parseInt(edge.target || '0', 10),
      type: 'plain' as const,
      label: `Connection from ${edge.source} to ${edge.target}`,
      description: `Connection from agent ${edge.source} to agent ${edge.target}`,
    }));

    setValue('edges', teamEdges);
  }, [edges, setValue]);

  // Handle connection between nodes
  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: Edge = {
        ...params,
        id: uuidv4(),
        type: 'removable', // use custom edge
        style: { stroke: '#7D40D9', strokeWidth: 2 },
        markerEnd: { type: 'arrowclosed', color: '#7D40D9' },
        data: {
          onRemove: (id: string) => {
            setEdges((eds) => eds.filter((e) => e.id !== id));

            // sync with form
            const formEdges: TeamEdge[] = edges
              .filter((e) => e.id !== id)
              .map((edge) => ({
                id: edge.id,
                source: parseInt(edge.source, 10),
                dest: parseInt(edge.target, 10),
                type: 'plain' as const,
                label: `edge-${edge.source}-${edge.target}`,
                description: `edge-${edge.source}-${edge.target}`,
              }));

            setValue('edges', formEdges);
          },
        },
      };

      setEdges((eds) => addEdge(newEdge, eds));
    },
    [edges, setEdges, setValue]
  );

  // Handle adding agent to canvas
  const handleAddAgent = useCallback(
    (template: SelectedTemplate) => {
      // Check if agent is already on canvas
      const existingNode = nodes.find((node) => node.id === template.id.toString());
      if (existingNode) {
        setSidebarOpen(false);
        return;
      }

      const newNode: Node = {
        id: template.id.toString(),
        type: 'agentNode',
        position: {
          x: Math.random() * 400 + 100,
          y: Math.random() * 300 + 100,
        },
        data: {
          label: template.name,
          description: template.description,
          model: template.model,
          templateId: template.id,
        },
      };

      setNodes((nds) => [...nds, newNode]);
      // setSidebarOpen(false);
    },
    [setNodes, nodes]
  );

  const onEdgesDelete = useCallback(
    (deleted: Edge[]) => {
      setEdges((eds) => eds.filter((e) => !deleted.some((d) => d.id === e.id)));

      // also sync with form
      const formEdges: TeamEdge[] = edges
        .filter((e) => !deleted.some((d) => d.id === e.id))
        .map((edge) => ({
          id: edge.id,
          source: parseInt(edge.source, 10),
          dest: parseInt(edge.target, 10),
          type: 'plain' as const,
          label: `edge-${edge.source}-${edge.target}`,
          description: `edge-${edge.source}-${edge.target}`,
        }));

      setValue('edges', formEdges);
    },
    [edges, setEdges, setValue]
  );

  // Pagination logic for selected templates
  const totalPages = Math.ceil(selectedTemplates.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const displayedTemplates = selectedTemplates.slice(startIndex, endIndex);

  // Sidebar content
  const sidebarContent = (
    <Box sx={{ width: 400, p: 2 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
        <Typography variant="h6">Available Agents</Typography>
        <IconButton onClick={() => setSidebarOpen(false)}>
          <Iconify icon="mingcute:close-line" />
        </IconButton>
      </Stack>

      <Divider sx={{ mb: 2 }} />

      <Stack spacing={2}>
        <Typography variant="subtitle2" sx={{ mb: 2 }}>
          Selected Agents ({selectedTemplates.length})
        </Typography>

        <Grid container spacing={2}>
          {displayedTemplates.map((template) => {
            const isOnCanvas = nodes.some((node) => node.id === template.id.toString());

            return (
              <Grid item xs={12} key={template.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: isOnCanvas ? '2px solid' : '1px solid',
                    borderColor: isOnCanvas ? 'success.main' : 'divider',
                    bgcolor: isOnCanvas ? alpha('#4caf50', 0.08) : 'background.paper',
                    '&:hover': {
                      bgcolor: isOnCanvas ? alpha('#4caf50', 0.12) : alpha('#7D40D9', 0.04),
                    },
                  }}
                  onClick={() => handleAddAgent(template)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Stack spacing={1}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Iconify
                          icon={template.category?.icon || 'mdi:robot'}
                          sx={{ color: 'primary.main' }}
                        />
                        <Typography variant="subtitle2" noWrap>
                          {template.name}
                        </Typography>
                        {isOnCanvas && (
                          <Chip label="Added" size="small" color="success" variant="outlined" />
                        )}
                      </Stack>
                      {template.description && (
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {template.description}
                        </Typography>
                      )}
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        {/* Pagination buttons */}
        <Stack direction="row" gap={2} justifyContent="end" sx={{ mt: 2 }}>
          <Button
            variant="contained"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 0))}
            disabled={currentPage === 0}
          >
            Previous
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages - 1))}
            disabled={currentPage >= totalPages - 1}
          >
            Next
          </Button>
        </Stack>

        {selectedTemplates.length === 0 && (
          <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mt: 4 }}>
            No agents selected. Please select agents in the previous step.
          </Typography>
        )}
      </Stack>
    </Box>
  );

  return (
    <Stack spacing={3}>
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography variant="h6" gutterBottom>
            Flow Builder
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Design your team workflow by connecting agents with arrows
          </Typography>
        </Box>

        <Button
          variant="contained"
          startIcon={<Iconify icon="mdi:plus" />}
          onClick={() => setSidebarOpen(true)}
          sx={{ bgcolor: '#7D40D9' }}
        >
          Add Agent
        </Button>
      </Stack>

      <Paper
        elevation={1}
        sx={{
          height: 600,
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2,
          overflow: 'hidden',
        }}
      >
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onEdgesDelete={onEdgesDelete}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          attributionPosition="bottom-left"
        >
          <Controls />
          <MiniMap />
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
        </ReactFlow>
      </Paper>

      {/* Sidebar Drawer */}
      <Drawer
        anchor="right"
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        PaperProps={{
          sx: { zIndex: 1300 },
        }}
      >
        {sidebarContent}
      </Drawer>
    </Stack>
  );
}

export default FlowBuilderStep;
