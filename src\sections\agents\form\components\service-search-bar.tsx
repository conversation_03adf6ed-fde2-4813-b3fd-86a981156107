import { TextField, InputAdornment } from '@mui/material';
import { Iconify } from 'src/components/iconify';

interface ServiceSearchBarProps {
  query: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

export default function ServiceSearchBar({ query, onChange, placeholder }: ServiceSearchBarProps) {
  return (
    <TextField
      fullWidth
      value={query}
      onChange={onChange}
      placeholder={placeholder || 'Search...'}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
          </InputAdornment>
        ),
      }}
    />
  );
}