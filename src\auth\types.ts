export type UserType = {
  id: number;
  misrajId?: string | null;
  email: string;
  name: string;
  username: string;
  pictureUrl?: string | null;
  roleId: number;
  createdAt: string;
  updatedAt: string;
  pictureFileId?: number | null;
  verifiedAt?: string | null;
  status: string;
  role: {
    id: number;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  };
  pictureFile?: any;
  permissions: string[];
  accessToken?: string;
} | null;

export type AuthState = {
  user: UserType;
  loading: boolean;
};

export type AuthContextValue = {
  user: UserType;
  loading: boolean;
  authenticated: boolean;
  unauthenticated: boolean;
  checkUserSession?: () => Promise<void>;
};
