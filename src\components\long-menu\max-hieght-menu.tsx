import { Box, Typography } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import * as React from 'react';

import { usePermissions } from 'src/auth/hooks/use-permissions';

import { Iconify } from '../iconify';

export type MaxHeightMenuPropsType = {
  options: {
    label: string;
    onClick: () => void;
    disabled?: boolean;
    icon?: string;
    color?: string;
    permissions?: string[];
    actionId?: string;
  }[];
  clickHandler?: () => void;
  id?: string;
};

function LongMenu({ options, clickHandler, id }: MaxHeightMenuPropsType) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const { hasPermission } = usePermissions();

  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    clickHandler?.();
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChoose = (onClick: () => void) => {
    onClick();
    handleClose();
  };

  return (
    <>
      <Box sx={{ textAlign: 'center' }}>
        <IconButton
          sx={{ color: 'primary.main' }}
          color={open ? 'inherit' : 'default'}
          aria-label="more"
          id={id || 'long-button'}
          aria-controls={open ? 'long-menu' : undefined}
          aria-expanded={open ? 'true' : undefined}
          aria-haspopup="true"
          onClick={handleClick}
        >
          <Iconify icon="eva:more-vertical-fill" />
        </IconButton>
      </Box>

      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{ '& .MuiPaper-root': { width: '200px' } }}
      >
        {options
          .filter((option) => {
            // Filter out options that user doesn't have permission for
            if (option.permissions && option.permissions.length > 0) {
              return hasPermission(option.permissions);
            }
            return true;
          })
          .map((option) => (
            <MenuItem
              key={option.label}
              onClick={() => handleChoose(option.onClick)}
              disabled={option?.disabled ? option?.disabled : false}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                color: option?.color,
              }}
              id={option.actionId}
            >
              {option.icon && <Iconify icon={option.icon} color={option.color ?? 'primary.main'} />}
              <Typography variant="body2">{option.label}</Typography>
            </MenuItem>
          ))}
      </Menu>
    </>
  );
}

export default LongMenu;
