import {
  Grid,
  Box,
  Typography,
} from '@mui/material';
import AiFileCard from './ai-file-card';

interface FileGridProps {
  files: Array<{
    name: string;
    icon: string;
    type: string;
    size?: number;
    modifiedAt?: Date;
    children?: any[];
  }>;
  onSelectFile?: (file: any) => void;
  selectedFiles?: Array<any>;
}

// Grid view component (simplified for AI steps)
function FileGridView({ files, onSelectFile, selectedFiles = [] }: FileGridProps) {
  return (
    <Grid container spacing={3}>
      {files.map((file) => {
        const isSelected = selectedFiles.some((selectedFile) => selectedFile.name === file.name);
        return (
          <Grid key={file.name} item xs={12} sm={6} md={4} lg={3}>
            <AiFileCard file={file} onClick={() => onSelectFile?.(file)} selected={isSelected} />
          </Grid>
        );
      })}
    </Grid>
  );
}

export default function AiFileGrid({ files, onSelectFile, selectedFiles = [] }: FileGridProps) {
  return (
    <Box sx={{ py: 2 }}>
      {files.length > 0 ? (
        <FileGridView files={files} onSelectFile={onSelectFile} selectedFiles={selectedFiles} />
      ) : (
        <Box sx={{ textAlign: 'center', py: 5 }}>
          <Typography variant="body1" color="text.secondary">
            No AI models found
          </Typography>
        </Box>
      )}
    </Box>
  );
}
