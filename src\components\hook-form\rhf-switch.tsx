import { Controller, useFormContext } from 'react-hook-form';
import { Switch, FormControl, FormControlLabel, FormHelperText } from '@mui/material';

// ----------------------------------------------------------------------

interface OptionType {
  value: string | number;
  label: string;
}

interface RHFSwitchProps {
  name: string;
  label?: string;
  options?: OptionType[];
  labelPlacement?: 'end' | 'start' | 'top' | 'bottom';
  helperText?: React.ReactNode;
  sx?: object;
  onBlur?: () => void;
}

export function RHFSwitch({
  name,
  label,
  options,
  labelPlacement = 'end',
  helperText,
  sx,
  onBlur,
  ...other
}: RHFSwitchProps) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <FormControl component="fieldset" error={!!error} sx={sx}>
          <FormControlLabel
            control={
              <Switch
                {...field}
                checked={field.value === 'ACTIVE' || field.value === true}
                onChange={(event) => {
                  // Handle both boolean and string values
                  if (options && options.length >= 2) {
                    field.onChange(event.target.checked ? options[0].value : options[1].value);
                  } else {
                    field.onChange(event.target.checked);
                  }
                }}
                onBlur={onBlur}
              />
            }
            label={
              label ||
              (options && options.length >= 2
                ? field.value === options[0].value
                  ? options[0].label
                  : options[1].label
                : 'Toggle')
            }
            labelPlacement={labelPlacement}
            {...other}
          />
          {(error || helperText) && (
            <FormHelperText>{error ? error.message : helperText}</FormHelperText>
          )}
        </FormControl>
      )}
    />
  );
}
