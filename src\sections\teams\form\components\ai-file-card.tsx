import { Box, Card, Stack, Typography } from '@mui/material';
import { Iconify } from 'src/components/iconify';

interface AiFileCardProps {
  file: {
    name: string;
    icon: string;
    type: string;
    size?: number;
    modifiedAt?: Date;
    children?: any[];
  };
  onClick?: () => void;
  selected?: boolean;
}

export default function AiFileCard({ file, onClick, selected = false }: AiFileCardProps) {
  const { name, icon, type, children } = file;
  const isFolder = type === 'folder';

  return (
    <Card
      onClick={onClick}
      sx={{
        p: 2,
        cursor: 'pointer',
        borderRadius: 2,
        boxShadow: selected ? (theme) => `0 8px 16px 0 ${theme.palette.success.mainChannel} 0.24` : (theme) => theme.customShadows.z8,
        transition: (theme) => theme.transitions.create(['box-shadow', 'transform', 'background-color'], {
          duration: theme.transitions.duration.shorter,
        }),
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: (theme) => theme.customShadows.z16,
          bgcolor: selected ? 'success.lighter' : 'background.neutral',
        },
        bgcolor: selected ? 'success.lighter' : 'background.neutral',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        background: selected ? 'success.lighter' : 'background.paper',
        position: 'relative',
        border: selected ? '2px solid' : '1px solid',
        borderColor: selected ? 'success.main' : 'divider',
      }}
    >
      {selected && (
        <Box
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            zIndex: 9,
            width: 28,
            height: 28,
            borderRadius: '50%',
            backgroundColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            // boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
          }}
        >
          <Iconify icon="eva:checkmark-fill" width={18} height={18} sx={{ color: 'success.darker' }} />
        </Box>
      )}

      <Box
        sx={{
          width: 80,
          height: 80,
          borderRadius: 1.5,
          mb: 2.5,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'transform 0.3s ease',
          color: 'inherit',
          ...(selected && {
            transform: 'scale(1.05)',
          
          }),
        }}
      >
        <Iconify  icon={icon} width={60} height={60} />
      </Box>

      <Stack spacing={0.5} sx={{ width: '100%', textAlign: 'center' }}>
        <Typography variant="subtitle2" fontWeight='bold' noWrap>
          {name}
        </Typography>

        <Box sx={{ color: 'text.secondary' }}>
          <Typography variant="caption" component="span">
            {isFolder ? `${children?.filter(child => child.type !== 'folder').length || 0} files` : type.toUpperCase()}
          </Typography>
        </Box>
      </Stack>
    </Card>
  );
}

