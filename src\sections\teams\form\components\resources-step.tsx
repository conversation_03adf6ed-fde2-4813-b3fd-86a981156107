import { useState } from 'react';
import { <PERSON>, Stack, Typo<PERSON>, Chip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { CONFIG } from 'src/config-global';
import FilePathBreadcrumbs from './file-path-breadcrumbs';
import FileSearchBar from './file-search-bar';
import FileGrid from './file-grid';

// Helper function to get file icon path
const getFileIconPath = (type: string) => {
  const basePath = CONFIG.site.basePath || '';

  switch (type) {
    case 'folder':
      return `${basePath}/assets/icons/files/ic-folder.svg`;
    case 'pdf':
      return `${basePath}/assets/icons/files/ic-pdf.svg`;
    case 'json':
      return `${basePath}/assets/icons/files/ic-file.svg`;
    case 'pptx':
      return `${basePath}/assets/icons/files/ic-power_point.svg`;
    case 'txt':
      return `${basePath}/assets/icons/files/ic-txt.svg`;
    case 'docx':
      return `${basePath}/assets/icons/files/ic-word.svg`;
    case 'xlsx':
      return `${basePath}/assets/icons/files/ic-excel.svg`;
    case 'png':
    case 'jpg':
    case 'jpeg':
      return `${basePath}/assets/icons/files/ic-img.svg`;
    case 'zip':
      return `${basePath}/assets/icons/files/ic-zip.svg`;
    case 'mp3':
    case 'wav':
    case 'audio':
      return `${basePath}/assets/icons/files/ic-audio.svg`;
    case 'mp4':
    case 'mov':
    case 'video':
      return `${basePath}/assets/icons/files/ic-video.svg`;
    default:
      return `${basePath}/assets/icons/files/ic-file.svg`;
  }
};

// Mock data for files
// This will be replaced with dynamic data from the backend in the future
const MOCK_FILES = [
  {
    name: 'Videos',
    path: getFileIconPath('folder'),
    type: 'folder',
    size: 1024 * 1024 * 15, // 15 MB
    modifiedAt: new Date('2023-10-10'),
    children: [
      {
        name: 'Product Demo.mp4',
        path: getFileIconPath('video'),
        type: 'mp4',
        size: 1024 * 1024 * 8, // 8 MB
        modifiedAt: new Date('2023-10-15'),
      },
      {
        name: 'Tutorial Videos',
        path: getFileIconPath('folder'),
        type: 'folder',
        size: 1024 * 1024 * 7, // 7 MB
        modifiedAt: new Date('2023-11-01'),
        children: [
          {
            name: 'Getting Started.mp4',
            path: getFileIconPath('video'),
            type: 'mp4',
            size: 1024 * 1024 * 3, // 3 MB
            modifiedAt: new Date('2023-11-02'),
          },
          {
            name: 'Advanced Features.mp4',
            path: getFileIconPath('video'),
            type: 'mp4',
            size: 1024 * 1024 * 4, // 4 MB
            modifiedAt: new Date('2023-11-03'),
          },
          {
            name: 'Resources',
            path: getFileIconPath('folder'),
            type: 'folder',
            size: 1024 * 1024 * 2, // 2 MB
            modifiedAt: new Date('2023-11-04'),
            children: [
              {
                name: 'Slides.pptx',
                path: getFileIconPath('pptx'),
                type: 'pptx',
                size: 1024 * 1024 * 1, // 1 MB
                modifiedAt: new Date('2023-11-05'),
              },
              {
                name: 'Script.docx',
                path: getFileIconPath('docx'),
                type: 'docx',
                size: 1024 * 512, // 512 KB
                modifiedAt: new Date('2023-11-06'),
              },
            ],
          },
        ],
      },
      {
        name: 'Tutorial.mp4',
        path: getFileIconPath('video'),
        type: 'mp4',
        size: 1024 * 1024 * 5, // 5 MB
        modifiedAt: new Date('2023-11-05'),
      },
      {
        name: 'Company Overview.mp4',
        path: getFileIconPath('video'),
        type: 'mp4',
        size: 1024 * 1024 * 2, // 2 MB
        modifiedAt: new Date('2023-10-12'),
      },
    ],
  },
  {
    name: 'Images',
    path: getFileIconPath('folder'),
    type: 'folder',
    size: 1024 * 1024 * 10, // 10 MB
    modifiedAt: new Date('2023-11-18'),
    children: [
      {
        name: 'Logo.png',
        path: getFileIconPath('png'),
        type: 'png',
        size: 1024 * 250, // 250 KB
        modifiedAt: new Date('2023-11-18'),
      },
      {
        name: 'Marketing Assets',
        path: getFileIconPath('folder'),
        type: 'folder',
        size: 1024 * 1024 * 3, // 3 MB
        modifiedAt: new Date('2023-11-15'),
        children: [
          {
            name: 'Social Media',
            path: getFileIconPath('folder'),
            type: 'folder',
            size: 1024 * 1024 * 1.5, // 1.5 MB
            modifiedAt: new Date('2023-11-16'),
            children: [
              {
                name: 'Facebook Banner.jpg',
                path: getFileIconPath('jpg'),
                type: 'jpg',
                size: 1024 * 500, // 500 KB
                modifiedAt: new Date('2023-11-17'),
              },
              {
                name: 'Instagram Post.jpg',
                path: getFileIconPath('jpg'),
                type: 'jpg',
                size: 1024 * 450, // 450 KB
                modifiedAt: new Date('2023-11-17'),
              },
              {
                name: 'Twitter Header.png',
                path: getFileIconPath('png'),
                type: 'png',
                size: 1024 * 350, // 350 KB
                modifiedAt: new Date('2023-11-17'),
              },
            ],
          },
          {
            name: 'Print Materials',
            path: getFileIconPath('folder'),
            type: 'folder',
            size: 1024 * 1024 * 1.5, // 1.5 MB
            modifiedAt: new Date('2023-11-16'),
            children: [
              {
                name: 'Brochure.pdf',
                path: getFileIconPath('pdf'),
                type: 'pdf',
                size: 1024 * 800, // 800 KB
                modifiedAt: new Date('2023-11-17'),
              },
              {
                name: 'Business Card.pdf',
                path: getFileIconPath('pdf'),
                type: 'pdf',
                size: 1024 * 200, // 200 KB
                modifiedAt: new Date('2023-11-17'),
              },
            ],
          },
        ],
      },
      {
        name: 'Banner.jpg',
        path: getFileIconPath('jpg'),
        type: 'jpg',
        size: 1024 * 780, // 780 KB
        modifiedAt: new Date('2023-11-19'),
      },
      {
        name: 'Team Photo.jpg',
        path: getFileIconPath('jpg'),
        type: 'jpg',
        size: 1024 * 1200, // 1.2 MB
        modifiedAt: new Date('2023-11-20'),
      },
      {
        name: 'Product Screenshot.png',
        path: getFileIconPath('png'),
        type: 'png',
        size: 1024 * 450, // 450 KB
        modifiedAt: new Date('2023-11-22'),
      },
    ],
  },
  {
    name: 'Audio',
    path: getFileIconPath('folder'),
    type: 'folder',
    size: 1024 * 1024 * 8, // 8 MB
    modifiedAt: new Date('2023-10-22'),
    children: [
      {
        name: 'Podcasts',
        path: getFileIconPath('folder'),
        type: 'folder',
        size: 1024 * 1024 * 5, // 5 MB
        modifiedAt: new Date('2023-10-22'),
        children: [
          {
            name: 'Season 1',
            path: getFileIconPath('folder'),
            type: 'folder',
            size: 1024 * 1024 * 3, // 3 MB
            modifiedAt: new Date('2023-10-23'),
            children: [
              {
                name: 'Episode 1.mp3',
                path: getFileIconPath('audio'),
                type: 'mp3',
                size: 1024 * 1024 * 1, // 1 MB
                modifiedAt: new Date('2023-10-24'),
              },
              {
                name: 'Episode 2.mp3',
                path: getFileIconPath('audio'),
                type: 'mp3',
                size: 1024 * 1024 * 1, // 1 MB
                modifiedAt: new Date('2023-10-25'),
              },
              {
                name: 'Episode 3.mp3',
                path: getFileIconPath('audio'),
                type: 'mp3',
                size: 1024 * 1024 * 1, // 1 MB
                modifiedAt: new Date('2023-10-26'),
              },
            ],
          },
          {
            name: 'Season 2',
            path: getFileIconPath('folder'),
            type: 'folder',
            size: 1024 * 1024 * 2, // 2 MB
            modifiedAt: new Date('2023-10-27'),
            children: [
              {
                name: 'Episode 1.mp3',
                path: getFileIconPath('audio'),
                type: 'mp3',
                size: 1024 * 1024 * 1, // 1 MB
                modifiedAt: new Date('2023-10-28'),
              },
              {
                name: 'Episode 2.mp3',
                path: getFileIconPath('audio'),
                type: 'mp3',
                size: 1024 * 1024 * 1, // 1 MB
                modifiedAt: new Date('2023-10-29'),
              },
            ],
          },
        ],
      },
      {
        name: 'Podcast Episode 1.mp3',
        path: getFileIconPath('audio'),
        type: 'mp3',
        size: 1024 * 1024 * 2, // 2 MB
        modifiedAt: new Date('2023-10-25'),
      },
      {
        name: 'Interview.mp3',
        path: getFileIconPath('audio'),
        type: 'mp3',
        size: 1024 * 1024 * 3, // 3 MB
        modifiedAt: new Date('2023-10-28'),
      },
      {
        name: 'Sound Effects.mp3',
        path: getFileIconPath('audio'),
        type: 'mp3',
        size: 1024 * 1024 * 1, // 1 MB
        modifiedAt: new Date('2023-10-30'),
      },
    ],
  },
  {
    name: 'Documents',
    path: getFileIconPath('folder'),
    type: 'folder',
    size: 1024 * 1024 * 5, // 5 MB
    modifiedAt: new Date('2023-10-10'),
    children: [
      {
        name: 'Reports',
        path: getFileIconPath('folder'),
        type: 'folder',
        size: 1024 * 1024 * 2, // 2 MB
        modifiedAt: new Date('2023-10-11'),
        children: [
          {
            name: 'Q1 Report.pdf',
            path: getFileIconPath('pdf'),
            type: 'pdf',
            size: 1024 * 512, // 512 KB
            modifiedAt: new Date('2023-10-12'),
          },
          {
            name: 'Q2 Report.pdf',
            path: getFileIconPath('pdf'),
            type: 'pdf',
            size: 1024 * 512, // 512 KB
            modifiedAt: new Date('2023-10-13'),
          },
          {
            name: 'Supporting Data',
            path: getFileIconPath('folder'),
            type: 'folder',
            size: 1024 * 1024 * 1, // 1 MB
            modifiedAt: new Date('2023-10-14'),
            children: [
              {
                name: 'Q1 Data.xlsx',
                path: getFileIconPath('xlsx'),
                type: 'xlsx',
                size: 1024 * 500, // 500 KB
                modifiedAt: new Date('2023-10-15'),
              },
              {
                name: 'Q2 Data.xlsx',
                path: getFileIconPath('xlsx'),
                type: 'xlsx',
                size: 1024 * 500, // 500 KB
                modifiedAt: new Date('2023-10-16'),
              },
            ],
          },
        ],
      },
      {
        name: 'Student Names.pdf',
        path: getFileIconPath('pdf'),
        type: 'pdf',
        size: 1024 * 512, // 512 KB
        modifiedAt: new Date('2023-10-15'),
      },
      {
        name: 'school_statistics.json',
        path: getFileIconPath('json'),
        type: 'json',
        size: 1024 * 128, // 128 KB
        modifiedAt: new Date('2023-10-20'),
      },
      {
        name: 'Presentation.pptx',
        path: getFileIconPath('pptx'),
        type: 'pptx',
        size: 1024 * 2048, // 2 MB
        modifiedAt: new Date('2023-10-25'),
      },
      {
        name: 'Notes.txt',
        path: getFileIconPath('txt'),
        type: 'txt',
        size: 1024 * 15, // 15 KB
        modifiedAt: new Date('2023-10-14'),
      },
    ],
  },
];

// Type definition for file object
interface FileObject {
  name: string;
  path: string;
  type: string;
  size: number;
  modifiedAt: Date;
  children?: FileObject[];
}

export default function ResourcesStep() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<FileObject[]>([]);
  const [currentPath, setCurrentPath] = useState<string[]>(['My Drive']);

  // We'll use these functions directly in the component

  // Initialize with only folders at root level
  const [currentItems, setCurrentItems] = useState<FileObject[]>(
    MOCK_FILES.filter((file) => file.type === 'folder')
  );

  // Track if we're viewing files inside a folder or the root folder list
  const [isViewingFiles, setIsViewingFiles] = useState(false);

  // Track the navigation history for breadcrumbs
  // This also serves as our way to track the current folder
  const [folderHistory, setFolderHistory] = useState<FileObject[]>([]);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle item selection (folder or file)
  const handleSelectFile = (file: FileObject) => {
    // If it's a folder, navigate into it
    if (file.type === 'folder') {
      // Update path to include the current folder
      setCurrentPath([...currentPath, file.name]);

      // Add current folder to history
      setFolderHistory([...folderHistory, file]);

      // Show both files and subfolders when opening a folder
      const folderContents = file.children || [];
      console.log('Folder contents:', folderContents); // Debug log
      setCurrentItems(folderContents);

      // Set flag that we're viewing files inside a folder
      setIsViewingFiles(true);

      // Don't clear selected files when navigating between folders
      return;
    }

    // Otherwise handle file selection (only for files, not folders)
    const isSelected = selectedFiles.some(
      (selectedFile) => selectedFile.path === file.path && selectedFile.name === file.name
    );

    if (isSelected) {
      setSelectedFiles(
        selectedFiles.filter(
          (selectedFile) => !(selectedFile.path === file.path && selectedFile.name === file.name)
        )
      );
    } else {
      setSelectedFiles([...selectedFiles, file]);
    }
  };

  // Handle breadcrumb navigation
  const handlePathChange = (index: number) => {
    // If clicking on the current path, do nothing
    if (index === currentPath.length - 1) return;

    // When clicking on "My Drive" (index 0), go back to root
    if (index === 0) {
      // Reset path to just "My Drive"
      setCurrentPath(['My Drive']);

      // Show only folders at root level
      setCurrentItems(MOCK_FILES.filter((file) => file.type === 'folder'));

      // Set flag that we're viewing the root folder list
      setIsViewingFiles(false);

      // Reset history
      setFolderHistory([]);

      // Don't clear selected files when navigating back
      return;
    }

    // For other indices, navigate to that specific level in the path
    const newPath = currentPath.slice(0, index + 1);
    setCurrentPath(newPath);

    // Update folder history
    const newHistory = folderHistory.slice(0, index);
    setFolderHistory(newHistory);

    // If we're going back to a previous folder
    if (index > 0) {
      const targetFolder = folderHistory[index - 1];
      // Show both files and subfolders when navigating to a folder
      const folderContents = targetFolder.children || [];
      setCurrentItems(folderContents);
      setIsViewingFiles(true);
    } else {
      // We're at root level - only show folders
      setCurrentItems(MOCK_FILES.filter((file) => file.type === 'folder'));
      setIsViewingFiles(false);
    }
  };

  // Function to clear all selected files
  const handleClearSelectedFiles = () => {
    setSelectedFiles([]);
  };

  // Filter items based on search query
  const filteredItems = currentItems.filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Debug log to check what items are being displayed
  console.log('Current items:', currentItems);
  console.log('Filtered items:', filteredItems);
  console.log('Is viewing files:', isViewingFiles);

  return (
    <Stack spacing={3}>
      <FileSearchBar
        query={searchQuery}
        onChange={handleSearchChange}
        placeholder={
          isViewingFiles
            ? t('components.teams.resources.searchFiles')
            : t('components.teams.resources.searchFolders')
        }
        sx={{
          mt: '22px',
          '& .MuiOutlinedInput-root': {
            borderRadius: '48px',
            border: '1px solid',
            borderColor: 'divider',
            background: 'background.paper',
          },
        }}
      />
      <Box
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          p: 3,
          borderRadius: 2,
          maxHeight: '60vh', // Set a maximum height
          overflow: 'hidden', // Hide overflow
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <FilePathBreadcrumbs
          paths={currentPath.map((name, index) => ({
            name,
            path: index === 0 ? '/my-drive' : `/my-drive/${name}`,
          }))}
          onPathClick={handlePathChange}
        />

        <Box
          sx={{
            overflow: 'auto',
            flex: 1,
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            msOverflowStyle: 'none' /* IE and Edge */,
            scrollbarWidth: 'none' /* Firefox */,
          }}
        >
          <FileGrid
            files={filteredItems}
            onSelectFile={handleSelectFile}
            selectedFiles={selectedFiles}
            isViewingFiles={isViewingFiles}
          />
        </Box>
        {selectedFiles.length > 0 && (
          <Box sx={{ backgroundColor: 'background.neutral', p: 2 }}>
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}
            >
              <Typography variant="subtitle2">
                {t('components.teams.resources.selectedFiles')} ({selectedFiles.length})
              </Typography>
              <Chip
                label={t('components.buttons.clearAll')}
                color="error"
                size="small"
                onClick={handleClearSelectedFiles}
                sx={{ cursor: 'pointer' }}
              />
            </Box>
            <Stack direction="row" spacing={1} flexWrap="wrap">
              {selectedFiles.map((file) => (
                <Chip
                  key={`${file.path}-${file.name}`}
                  label={file.name}
                  onDelete={() => handleSelectFile(file)}
                  sx={{ mb: 1, backgroundColor: 'rgba(255, 226, 216, 1)', color: 'black' }}
                />
              ))}
            </Stack>
          </Box>
        )}
      </Box>
    </Stack>
  );
}
