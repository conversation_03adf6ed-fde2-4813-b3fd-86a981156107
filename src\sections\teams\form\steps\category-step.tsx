import { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  Typography,
  Grid,
  Card,
  CardContent,
  Radio,
  FormControlLabel,
  RadioGroup,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
import { useCategoriesApi, CategoriesQueryParams } from 'src/services/api/use-categories-api';
import { useDebounce } from 'src/hooks/use-debounce';
import { TeamFormValues } from '../../view/use-teams-view';
import ServiceSearchBar from '../../../agents/form/components/service-search-bar';

// ----------------------------------------------------------------------

interface CategoryStepProps {
  // Add any specific props if needed
}

export function CategoryStep(_props: CategoryStepProps) {
  const { setValue, watch } = useFormContext<TeamFormValues>();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentQuery, setCurrentQuery] = useState<string | null>(null);

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 1000);

  // Watch current selection
  const selectedCategoryId = watch('categoryId');

  // Update current query when debounced search changes
  useEffect(() => {
    const trimmedQuery = debouncedSearchQuery.trim();
    setCurrentQuery(trimmedQuery === '' ? null : trimmedQuery);
  }, [debouncedSearchQuery]);

  // Build query parameters for API
  const queryParams: CategoriesQueryParams = {
    take: 15,
    skip: 0,
    ...(currentQuery && { name: currentQuery }),
  };

  // Get categories from API with search parameters
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesResponse, isLoading } = useGetCategories(queryParams);
  const categories = categoriesResponse?.categories || [];

  // Handle category selection
  const handleCategorySelect = (categoryId: number) => {
    setValue('categoryId', categoryId, { shouldValidate: true });
  };

  // Handle search input change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Select a category for your team
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search categories..."
        />
        <RadioGroup
          value={selectedCategoryId || ''}
          onChange={(event) => handleCategorySelect(Number(event.target.value))}
        >
          <Grid container spacing={2}>
            {categories.map((category) => {
              const isSelected = selectedCategoryId === category.id;
              return (
                <Grid item xs={6} key={category.id}>
                  <Card
                    variant="outlined"
                    sx={{
                      cursor: 'pointer',
                      bgcolor: 'divider',
                      border: isSelected ? '2px solid' : '1px solid',
                      borderColor: isSelected ? 'primary.main' : 'divider',
                    }}
                    onClick={() => handleCategorySelect(category.id)} 
                  >
                    <CardContent>
                      <FormControlLabel
                        control={
                          <Radio
                            checked={isSelected}
                            value={category.id} 
                          />
                        }
                        label={
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Iconify
                              icon={category.icon}
                              sx={{ color: category.theme || 'primary.main' }}
                            />
                            <Stack>
                              <Typography variant="subtitle2">{category.name}</Typography>
                              {category.description && (
                                <Typography variant="caption" color="text.secondary">
                                  {category.description}
                                </Typography>
                              )}
                            </Stack>
                          </Stack>
                        }
                        sx={{ width: '100%', margin: 0 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </RadioGroup>
      </Stack>
    </>
  );
}

export default CategoryStep;