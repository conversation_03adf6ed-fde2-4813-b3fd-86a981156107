import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for MyTeamTemplatesss
export const MyTeamTemplatesEndpoints = {
  list: '/user-teams',
  details: '/user-teams',
  reconfigure: (id: string) => `/user-teams/${id}/reconfigure`,
};

// Define the Category interface

// Define filters interface for MyTeamTemplatess API
export interface MyTeamTemplatesFilters {
  type?: string;
  categoryId?: number;
  status?: string;
  name?: string;
  take?: number;
  skip?: number;
}

export interface DataTemplatesTeam {
  id?: number;
  templateTeamId?: number;
  specialRequest?: string; // Optional
  toolConfigIds?: {
    toolConfigId: number;
  }[];

  newToolConfigs?: {
    toolId: number;
    name: string;
    config: { prompt: string; temperature: number };
  }[];
}

export interface GetUserTeamsResponse {
  userTeams: UserTeam[];
  total: number;
}

export interface UserTeam {
  id: number;
  userId: number;
  templateTeamId: number;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  templateTeam: TemplateTeam;
  teamTools: {
    id: number;
    agentId: number;
    toolId: number;
    toolConfigId: number;
    createdAt: string;
    updatedAt: string;
    tool: {
      id: number;
      name: string;
      description: string;
      url: null;
      fields: null;
      icon: string;
      createdAt: string;
      updatedAt: string;
    };
    toolConfig: {
      id: number;
      toolId: number;
      name: string;
      createdAt: string;
      updatedAt: string;
    };
  }[];
}

export interface TemplateTeam {
  id: number;
  name: string;
  categoryId: number;
  description: string;
  userId: number;
  visibility: 'PUBLIC' | 'PRIVATE';
  status: 'ACTIVE' | 'INACTIVE';
  type: 'AUTO' | 'MANUAL';
  model: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  category: Category;
}

export interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  theme: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

// Define the API response structure

// Create a hook to use the MyTeamTemplatesss API
export const useMyTeamTemplatesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all MyTeamTemplatesss with optional filters
  const useGetAllMyTeamTemplates = (filters?: MyTeamTemplatesFilters) => {
    return apiServices.useGetListService<GetUserTeamsResponse, MyTeamTemplatesFilters>({
      url: MyTeamTemplatesEndpoints.list,
      params: filters,
    });
  };

  // Get a single MyTeamTemplatess by ID
  const useGetOneMyTeamTemplate = (id: string) => {
    return apiServices.useGetItemService<UserTeam>({
      url: MyTeamTemplatesEndpoints.details,
      id,
    });
  };

  // Create a new MyTeamTemplatess
  const useCreateMyTeamTemplates = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<DataTemplatesTeam, { data: UserTeam }>({
      url: MyTeamTemplatesEndpoints.list,
      onSuccess,
    });
  };

  // Update a MyTeamTemplatess
  const useUpdateMyTeamTemplates = (id: string, onSuccess?: () => void) => {
    return apiServices.usePutService<DataTemplatesTeam>({
      url: MyTeamTemplatesEndpoints.reconfigure(id),
      onSuccess,
      withFormData: false,
    });
  };

  // Delete a MyTeamTemplatess
  const useDeleteMyTeamTemplates = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any, number>({
      url: MyTeamTemplatesEndpoints.details,
      urlAfterSuccess: MyTeamTemplatesEndpoints.details + 'list',
      onSuccess,
    });
  };

  return {
    useGetAllMyTeamTemplates,
    useGetOneMyTeamTemplate,
    useCreateMyTeamTemplates,
    useUpdateMyTeamTemplates,
    useDeleteMyTeamTemplates,
  };
};
