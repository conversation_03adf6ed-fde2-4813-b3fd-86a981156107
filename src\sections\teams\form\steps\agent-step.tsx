import { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>ack,
  Typography,
  Grid,
  Card,
  CardContent,
  Checkbox,
  alpha,
  Button,
  Box,
  IconButton,
} from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { useTemplatesApi, TemplatesQueryParams } from 'src/services/api/use-templates-api';
import { useDebounce } from 'src/hooks/use-debounce';
import { useUrlParams } from 'src/hooks/use-url-params';
import { Iconify } from 'src/components/iconify';
import { EnhancedTemplateTeam, TeamFormValues } from '../../view/use-teams-view';
import ServiceSearchBar from '../../../agents/form/components/service-search-bar';

// ----------------------------------------------------------------------

interface TemplateItem {
  id: number; // تأكد من أن هذا هو نوع id
  name: string;
  category: {
    icon: string;
  };
}

interface AgentStepProps {
  team: EnhancedTemplateTeam | null;
}

export function AgentStep({ team }: AgentStepProps) {
  const { setValue, getValues } = useFormContext<TeamFormValues>();

  const [activeQuery, setActiveQuery] = useState<string | null>(null);
  const { params, updateParams } = useUrlParams({
    defaultTake: 50,
    defaultSkip: 0,
  });

  const [rawSearch, setRawSearch] = useState(params.name || '');
  const debouncedSearch = useDebounce(rawSearch, 1000);

  const [chosenTemplates, setChosenTemplates] = useState<number[]>([]);
  const [initialChosenTemplates, setInitialChosenTemplates] = useState<number[]>([]); // حالة جديدة
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 5;

  useEffect(() => {
    const cleaned = debouncedSearch.trim();
    setActiveQuery(cleaned === '' ? null : cleaned);
    updateParams({ name: cleaned || null, skip: 0 });
  }, [debouncedSearch, updateParams]);

  const queryParams: TemplatesQueryParams = {
    take: params.take,
    skip: params.skip,
    ...(activeQuery && { name: activeQuery }),
  };

  const { useGetTemplates } = useTemplatesApi();
  const { data: templatesResponse } = useGetTemplates(queryParams);
  const templateItems: TemplateItem[] = templatesResponse?.templates || [];

  // Hydrate selection from form state or initial team only once (do not depend on templateItems/search)
  useEffect(() => {
    const existingIds = getValues('templatesIds') || [];
    if (existingIds.length > 0) {
      setChosenTemplates(existingIds);
      setInitialChosenTemplates(existingIds);
    } else {
      const teamTemplates = team?.templatesInTeam?.map((item) => item.template).flat() || [];
      const ids = teamTemplates.map((t) => t.id);
      setChosenTemplates(ids);
      setInitialChosenTemplates(ids);
      setValue('templatesIds', ids, { shouldValidate: true });
    }
  }, [team, getValues, setValue]);

  // Keep selectedTemplates data in sync across searches/pagination
  useEffect(() => {
    const prevSelected = (getValues('selectedTemplates') as TemplateItem[]) || [];
    const existingMap = new Map<number, TemplateItem>(prevSelected.map((t) => [t.id, t]));

    // Enrich with any currently loaded items
    templateItems.forEach((t) => {
      if (chosenTemplates.includes(t.id)) {
        existingMap.set(t.id, t);
      }
    });

    // Keep only those currently selected, preserve stable order of chosenTemplates
    const nextSelected = chosenTemplates
      .map((id) => existingMap.get(id))
      .filter((t): t is TemplateItem => Boolean(t));

    setValue('selectedTemplates', nextSelected, { shouldValidate: true });
  }, [chosenTemplates, templateItems, getValues, setValue]);

  // Optional: keep initially chosen ordering updated so selected cards show first
  useEffect(() => {
    setInitialChosenTemplates(chosenTemplates);
  }, [chosenTemplates]);

  const handleTemplateToggle = (templateId: number) => {
    const current = [...chosenTemplates];
    const nextSelection = current.includes(templateId)
      ? current.filter((id) => id !== templateId)
      : [...current, templateId];

    setChosenTemplates(nextSelection);
    setValue('templatesIds', nextSelection.length > 0 ? nextSelection : [], {
      shouldValidate: true,
    });
  };

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setRawSearch(event.target.value);
  }, []);

  const totalPages = Math.ceil(templateItems.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  // دمج initialChosenTemplates مع templateItems
  const displayedTemplates = [
    ...templateItems.filter((item) => initialChosenTemplates.includes(item.id)), // العناصر المختارة أولاً
    ...templateItems.filter((item) => !initialChosenTemplates.includes(item.id)), // العناصر الأخرى
  ].slice(startIndex, endIndex); // التصفح

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Select agents for your team
      </Typography>

      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={rawSearch}
          onChange={handleSearchChange}
          placeholder="Search agents..."
        />

        <Typography variant="subtitle2" sx={{ mb: 2 }}>
          Selected Agents ({chosenTemplates.length})
        </Typography>

        <Grid container spacing={2}>
          {displayedTemplates.map((tpl) => {
            const isChecked = chosenTemplates.includes(tpl.id);

            return (
              <Grid item xs={12} sm={6} md={12} key={tpl.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: isChecked ? '2px solid' : '1px solid',
                    borderColor: isChecked ? 'primary.main' : 'divider',
                    bgcolor: isChecked ? alpha('#7D40D9', 0.08) : 'background.paper',
                  }}
                  onClick={() => handleTemplateToggle(tpl.id)}
                >
                  <CardContent sx={{ p: 2 }}>
                    <Stack spacing={1}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Checkbox
                          checked={isChecked}
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTemplateToggle(tpl.id);
                          }}
                        />

                        <Typography variant="subtitle2" noWrap>
                          <Iconify sx={{ my: '-4px' }} icon={tpl.category.icon} />
                          {tpl.name}
                        </Typography>
                      </Stack>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <IconButton
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 0))}
            disabled={currentPage === 0}
          >
            <Iconify icon="eva:arrow-back-outline" />
          </IconButton>
          <IconButton
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages - 1))}
            disabled={currentPage >= totalPages - 1}
          >
            <Iconify icon="eva:arrow-forward-outline" />
          </IconButton>
        </Box>
      </Stack>
    </>
  );
}

export default AgentStep;
