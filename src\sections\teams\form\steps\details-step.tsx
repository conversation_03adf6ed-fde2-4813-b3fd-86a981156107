import { Stack, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { Field } from 'src/components/hook-form/fields';

// ----------------------------------------------------------------------

// Team status options (matching agent status structure)
const TEAM_STATUS_OPTIONS = [
  {
    value: 'ACTIVE',
    label: 'Active',
  },
  {
    value: 'DISABLED',
    label: 'Disabled',
  },
];

interface DetailsStepProps {
  // Add any specific props if needed
}

export function DetailsStep(_props: DetailsStepProps) {
  const { setValue } = useFormContext();

  const handleConfigGenerated = (config: { name: string; description: string }) => {
    // Update form fields with generated config
    setValue('name', config.name);
    setValue('description', config.description);
  };
  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Add your teams template details
      </Typography>

      <Stack spacing={3} sx={{ backgroundColor: 'white', p: 5, borderRadius: '10px' }}>
        <Typography my="-10px">Team Name</Typography>
        <Field.Text name="name" placeholder="Type your team name" />

        <Typography my="-10px">Description</Typography>
        <Field.Text name="description" placeholder="Type your team description" multiline />

        <Typography>Team Status</Typography>
        <Field.Switch
          sx={{ px: 5 }}
          name="status"
          options={TEAM_STATUS_OPTIONS}
          labelPlacement="end"
          onBlur={() => console.log('Blur event')}
        />
      </Stack>
    </>
  );
}

export default DetailsStep;
