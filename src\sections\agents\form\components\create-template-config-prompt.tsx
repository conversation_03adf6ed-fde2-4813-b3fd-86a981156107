import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Box,
  Stack,
  CircularProgress,
  Toolt<PERSON>,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { useTemplatesApi } from 'src/services/api/use-templates-api';

// ----------------------------------------------------------------------

interface CreateTemplateConfigPromptProps {
  onConfigGenerated?: (config: {
    name: string;
    description: string;
    systemMessage: string;
  }) => void;
}

export default function CreateTemplateConfigPrompt({
  onConfigGenerated,
}: CreateTemplateConfigPromptProps) {
  const [description, setDescription] = useState('');

  const { useCreateConfig } = useTemplatesApi();
  const { mutate: createConfig, isPending } = useCreateConfig();

  const handleGenerate = () => {
    if (description.trim()) {
      createConfig(
        { description: description.trim() },
        {
          onSuccess: (data) => {
            // Reset the description field after successful generation
            setDescription('');
            // Call the callback with the generated config
            if (onConfigGenerated) {
              onConfigGenerated({
                name: data?.data?.name,
                description: data?.data?.description, // Note: API returns 'desciption' (typo in API)
                systemMessage: data?.data?.systemMessage,
              });
            }
          },
        }
      );
    }
  };

  const handleDescriptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDescription(event.target.value);
  };

  return (
    <Box
      sx={{
        backgroundColor: '#7D40D9',
        borderRadius: '12px',
        p: 2,
      }}
    >
      <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
        <Iconify icon="mdi:thunder" sx={{ color: 'white', fontSize: 24 }} />
        <Typography variant="h6" sx={{ color: 'white', fontWeight: 600 }}>
          Generate with AI
        </Typography>
      </Stack>

      <Stack spacing={2}>
        <TextField
          fullWidth
          value={description}
          onChange={handleDescriptionChange}
          placeholder="Describe what you want from this agent..."
          disabled={isPending}
          multiline
          InputProps={{
            endAdornment: (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                <Iconify icon="pepicons-pencil:line-y" sx={{ color: '#8c8484' }} />
                {isPending ? (
                  <CircularProgress />
                ) : (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleGenerate}
                      disabled={isPending || !description.trim()}
                    >
                      <Tooltip title="create template configure by AI">
                        <Typography color="primary" sx={{ cursor: 'pointer' }}>
                          Generate
                        </Typography>
                      </Tooltip>
                    </IconButton>
                  </InputAdornment>
                )}
              </Box>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'white',
              borderRadius: '8px',
              '& fieldset': {
                borderColor: 'rgba(255, 255, 255, 0.3)',
              },
              '&:hover fieldset': {
                borderColor: 'rgba(255, 255, 255, 0.5)',
              },
              '&.Mui-focused fieldset': {
                borderColor: 'black',
              },
              '& input, & textarea': {
                color: '#939394',
                '&::placeholder': {
                  color: '#939394',
                  opacity: 1,
                },
              },
            },
          }}
        />
      </Stack>
    </Box>
  );
}
