import { useState } from 'react';
import {
  Grid,
  Box,
  ToggleButtonGroup,
  ToggleButton,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import FileCard from './file-card';

interface FileGridProps {
  files: Array<{
    name: string;
    path: string;
    type: string;
    size?: number;
    modifiedAt?: Date;
    children?: any[];
  }>;
  onSelectFile?: (file: any) => void;
  selectedFiles?: Array<any>;
  isViewingFiles?: boolean; // New prop to indicate if we're viewing files inside a folder
}

// Table view component
function FileTable({
  files,
  onSelectFile,
  selectedFiles = [],
  isViewingFiles = false,
}: FileGridProps) {
  // Debug log to check what files are being displayed in table view
  console.log('FileTable - files:', files);
  console.log('FileTable - isViewingFiles:', isViewingFiles);

  return (
    <Table>
      <TableHead>
        <TableRow>
          <TableCell>Name</TableCell>
          <TableCell>Type</TableCell>
          <TableCell>Size</TableCell>
          <TableCell>Date</TableCell>
          <TableCell>Status</TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {files.map((file, index) => {
          const isSelected = selectedFiles.some(
            (selectedFile) => selectedFile.path === file.path && selectedFile.name === file.name
          );
          const isFolder = file.type === 'folder';

          // Make all items clickable
          const isSelectable = true;

          return (
            <TableRow
              key={`${file.path}-${file.name}`}
              onClick={isSelectable ? () => onSelectFile?.(file) : undefined}
              sx={{
                cursor: isSelectable ? 'pointer' : 'default',
                bgcolor: isSelected ? 'primary.lighter' : 'inherit',
                '&:hover': {
                  bgcolor: isSelectable
                    ? isSelected
                      ? 'primary.lighter'
                      : 'background.neutral'
                    : 'inherit',
                },
              }}
            >
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    component="img"
                    src={file.path}
                    sx={{
                      width: 36,
                      height: 36,
                      borderRadius: isFolder ? 1 : 0,
                      mr: 2,
                    }}
                  />
                  <Typography variant="body2">{file.name}</Typography>
                </Box>
              </TableCell>
              <TableCell>{file.type === 'folder' ? 'Folder' : file.type.toUpperCase()}</TableCell>
              <TableCell>
                {file.size
                  ? file.type === 'folder'
                    ? `${(file.size / (1024 * 1024)).toFixed(2)} MB`
                    : `${(file.size / 1024).toFixed(2)} KB`
                  : '-'}
              </TableCell>
              <TableCell>
                {file.modifiedAt ? new Date(file.modifiedAt).toLocaleDateString() : '-'}
              </TableCell>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: () => {
                        const colors = ['success.main', 'warning.main', 'error.main'];
                        return colors[index % colors.length];
                      },
                      mr: 1,
                    }}
                  />
                  <Typography variant="caption">
                    {index % 3 === 0 ? 'Active' : index % 3 === 1 ? 'Pending' : 'Inactive'}
                  </Typography>
                </Box>
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}

// Grid view component (original implementation)
function FileGridView({
  files,
  onSelectFile,
  selectedFiles = [],
  isViewingFiles = false,
}: FileGridProps) {
  // Debug log to check what files are being displayed
  console.log('FileGridView - files:', files);
  console.log('FileGridView - isViewingFiles:', isViewingFiles);

  return (
    <Grid container spacing={3}>
      {files.map((file) => {
        const isSelected = selectedFiles.some(
          (selectedFile) => selectedFile.path === file.path && selectedFile.name === file.name
        );
        return (
          <Grid key={`${file.path}-${file.name}`} item xs={6} sm={6} md={3}>
            <FileCard
              file={file}
              onClick={() => onSelectFile?.(file)}
              selected={isSelected}
              isViewingFiles={isViewingFiles}
            />
          </Grid>
        );
      })}
    </Grid>
  );
}

export default function FileGrid({
  files,
  onSelectFile,
  selectedFiles = [],
  isViewingFiles = false,
}: FileGridProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  const handleViewChange = (
    _: React.MouseEvent<HTMLElement>,
    newViewMode: 'grid' | 'table' | null
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewChange}
          aria-label="view mode"
          size="small"
          sx={{
            '& .MuiToggleButtonGroup-grouped': {
              border: '1px solid',
              borderColor: 'divider',
              '&.Mui-selected': {
                borderColor: 'primary.main',
                color: 'text.primary',
                bgcolor: 'action.selected',
              },
            },
          }}
        >
          <ToggleButton value="grid" aria-label="grid view">
            <Iconify
              icon="material-symbols:grid-view"
              width={24}
              height={24}
              sx={{ color: 'text.primary' }}
            />
          </ToggleButton>
          <ToggleButton value="table" aria-label="table view">
            <Iconify
              icon="material-symbols:table-rows-outline"
              width={24}
              height={24}
              sx={{ color: 'text.primary' }}
            />
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {viewMode === 'grid' ? (
        <FileGridView
          files={files}
          onSelectFile={onSelectFile}
          selectedFiles={selectedFiles}
          isViewingFiles={isViewingFiles}
        />
      ) : (
        <FileTable
          files={files}
          onSelectFile={onSelectFile}
          selectedFiles={selectedFiles}
          isViewingFiles={isViewingFiles}
        />
      )}
    </Box>
  );
}
