import { Box, Card, Stack, Typography } from '@mui/material';

interface FileCardProps {
  file: {
    name: string;
    path: string;
    type: string;
    size?: number;
    modifiedAt?: Date;
    children?: any[];
  };
  onClick?: () => void;
  selected?: boolean;
  isViewingFiles?: boolean; // New prop to indicate if we're viewing files inside a folder
}

export default function FileCard({
  file,
  onClick,
  selected = false,
  isViewingFiles = false,
}: FileCardProps) {
  const { name, path, type } = file;
  const isFolder = type === 'folder';

  // All items are clickable

  // Get file extension for display
  const getFileExtension = (fileName: string) => {
    const parts = fileName.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toUpperCase() : '';
  };

  // Get file type display name
  const getFileTypeName = () => {
    if (isFolder) {
      return '';
    }

    switch (type) {
      case 'pdf':
        return 'PDF';
      case 'docx':
        return 'DOC';
      case 'xlsx':
        return 'XLS';
      case 'pptx':
        return 'PPT';
      case 'txt':
        return 'TXT';
      case 'mp3':
      case 'wav':
        return 'Audio';
      case 'mp4':
      case 'mov':
        return 'Video';
      case 'png':
      case 'jpg':
      case 'jpeg':
        return 'Image';
      default:
        return getFileExtension(name);
    }
  };

  return (
    <Card
      onClick={onClick}
      sx={{
        p: 2,
        cursor: 'pointer',
        borderRadius: 1.5,
        transition: (theme) => theme.transitions.create('all'),
        '&:hover': {
          bgcolor: selected ? 'rgba(255, 226, 216, 1)' : 'background.neutral',
        },
        display: 'flex',
        flexDirection: 'column',
        my: '20px',
        alignItems: 'center',
        height: isViewingFiles ? '140px' : '160px',
        width: '100%',
        bgcolor: selected ? 'rgba(255, 226, 216, 1)' : 'transparent',

        boxShadow: (theme) => theme.customShadows.z1,
      }}
    >
      <Box
        component="img"
        src={path}
        sx={{
          width: isViewingFiles ? 60 : 80,
          height: isViewingFiles ? 60 : 80,
          borderRadius: isFolder ? 1 : 0,
          mb: 2,
          objectFit: 'contain',
        }}
      />

      <Stack spacing={0.5} sx={{ width: '100%', textAlign: 'center' }}>
        <Typography
          variant="body2"
          noWrap
          sx={{
            fontWeight: 500,
            fontSize: '0.875rem',
          }}
        >
          {name}
        </Typography>

        {!isFolder && (
          <Typography
            variant="caption"
            component="span"
            sx={{
              color: 'text.secondary',
              fontSize: '0.75rem',
            }}
          >
            {getFileTypeName()}
          </Typography>
        )}
      </Stack>
    </Card>
  );
}
