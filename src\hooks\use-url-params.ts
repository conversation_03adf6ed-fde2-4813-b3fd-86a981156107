import { useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

interface UseUrlParamsOptions {
  defaultTake?: number;
  defaultSkip?: number;
}

export function useUrlParams(options: UseUrlParamsOptions = {}) {
  const { defaultTake = 10, defaultSkip = 0 } = options;
  const [searchParams, setSearchParams] = useSearchParams();

  // Get current params with defaults
  const params = useMemo(() => {
    const take = parseInt(searchParams.get('take') || defaultTake.toString(), 10);
    const skip = parseInt(searchParams.get('skip') || defaultSkip.toString(), 10);
    const name = searchParams.get('name') || '';
    const categoryId = searchParams.get('categoryId') || '';
    const type = searchParams.get('type') || '';
    const model = searchParams.get('model') || '';
    const status = searchParams.get('status') || '';
    const toolName = searchParams.get('toolName') || '';
    const publishRequestStatus = searchParams.get('publishRequestStatus') || '';

    return {
      take,
      skip,
      name,
      categoryId,
      type,
      model,
      status,
      toolName,
      publishRequestStatus,
    };
  }, [searchParams, defaultTake, defaultSkip]);

  // Update a single parameter
  const updateParam = useCallback(
    (key: string, value: string | number | null) => {
      const newParams = new URLSearchParams(searchParams);

      if (value === null || value === '' || value === undefined) {
        newParams.delete(key);
      } else {
        newParams.set(key, value.toString());
      }

      setSearchParams(newParams);
    },
    [searchParams, setSearchParams]
  );

  // Update multiple parameters
  const updateParams = useCallback(
    (updates: Record<string, string | number | null>) => {
      const newParams = new URLSearchParams(searchParams);

      Object.entries(updates).forEach(([key, value]) => {
        if (value === null || value === '' || value === undefined) {
          newParams.delete(key);
        } else {
          newParams.set(key, value.toString());
        }
      });

      setSearchParams(newParams);
    },
    [searchParams, setSearchParams]
  );

  // Handle page change (for pagination)
  const handlePageChange = useCallback(
    (page: number, pageSize?: number) => {
      // page is 0-based from Material-UI's TablePagination
      const take = pageSize || params.take;

      // Use page directly as the skip value (page index)
      // For example:
      // Page 0 (first page): skip = 0
      // Page 1 (second page): skip = 1
      // Page 2 (third page): skip = 2
      const newSkip = page;

      console.log(
        `handlePageChange: Changing to page ${page} (0-based), skip=${newSkip}, take=${take}`
      );
      console.log(`handlePageChange: API will be called with: ?take=${take}&skip=${newSkip}`);

      // Create an update object with only the necessary parameters
      const updateObj: Record<string, string | number | null> = {
        skip: Math.max(0, newSkip),
      };

      // Only include take if it's different from the current take
      if (pageSize && pageSize !== params.take) {
        updateObj.take = pageSize;
      }

      console.log('handlePageChange: Updating URL params with:', updateObj);

      // Update URL params with the new skip value
      // This will trigger a re-render and a new API call with the updated skip value
      updateParams(updateObj);
    },
    [params.take, updateParams]
  );

  return {
    params,
    updateParam,
    updateParams,
    handlePageChange,
  };
}
