import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for templates
export const TemplateEndpoints = {
  list: '/templates',
  details: '/templates',
};

// Define the Category interface

// Define the Template data type based on the new API response
export interface TemplateTool {
  id: number;
  templateId: number;
  toolId: number;
  createdAt: string;
  tool: {
    id: number;
    icon: string;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  };
}

interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  theme: string;
  createdAt: string;
  updatedAt: string;
}

export interface Template {
  id: number;
  creatorId: number;
  name: string;
  description: string;
  categoryId: number;
  type: string; // or more specific: "SINGLE" | "OTHER_TYPE" if you know all possible values
  model: string;
  status: string; // or more specific: "ACTIVE" | "INACTIVE" etc.
  createdAt: string;
  updatedAt: string;
  systemMessage: string;
  visibility: string; // or more specific: "PUBLIC" | "PRIVATE" etc.
  category: Category;
  publishRequestStatus: string;
  templateTools: TemplateTool[];
}

export interface TemplatesResponse {
  templates: Template[];
  total: number;
}

// Define the API response structure

// Define filter parameters interface
export interface TemplateFilters {
  visibility?: 'PUBLIC' | 'PRIVATE';
  type?: string;
  category?: string;
  categoryId?: number;
  tools?: string;
  model?: string;
  status?: string;
  name?: string;
  take?: number;
  skip?: number;
}

// Create a hook to use the templates API
export const useTemplatesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all templates with optional filters
  const useGetTemplates = (filters?: TemplateFilters) => {
    return apiServices.useGetListService<TemplatesResponse, TemplateFilters>({
      url: TemplateEndpoints.list,
      params: filters,
    });
  };

  // Get a single template by ID
  const useGetTemplate = (id: string) => {
    return apiServices.useGetItemService<Template>({
      url: TemplateEndpoints.details,
      id: id?.toString(),
      queryOptions: {
        enabled: !!id,
      },
    });
  };

  // Create a new template
  const useCreateTemplate = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<Template, any>({
      url: TemplateEndpoints.list,
      onSuccess,
    });
  };

  const usePublishTemplate = (id: string | number, onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<{}, any>({
      url: `/templates/${id}/publish-request`,
      onSuccess,
    });
  };

  const useCreateConfig = (onSuccess?: (data: Template) => void) => {
    return apiServices.usePostService<
      { description: string },
      { data: { name: string; description: string; systemMessage: string } }
    >({
      url: `/templates/create-template-config-prompt`,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a template
  const useUpdateTemplate = (id: string | number, onSuccess?: () => void) => {
    return apiServices.usePatchService<Template>({
      url: TemplateEndpoints.details,
      id: id?.toString(),
      onSuccess,
    });
  };

  // Delete a template
  const useDeleteTemplate = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any, number | string>({
      url: TemplateEndpoints.details,
      onSuccess,
    });
  };

  return {
    useGetTemplates,
    useGetTemplate,
    useCreateTemplate,
    useUpdateTemplate,
    useDeleteTemplate,
    useCreateConfig,
    usePublishTemplate,
  };
};
