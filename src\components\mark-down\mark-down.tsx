import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { Box, Divider, Paper, Typography } from '@mui/material';

export const StyledMarkdown = ({ content }: { content: string }) => (
  <ReactMarkdown
    remarkPlugins={[remarkGfm]}
    rehypePlugins={[rehypeHighlight]}
    components={{
      // Headings
      h1: ({ children }: any) => (
        <Typography variant="h4" component="h1" sx={{ mb: 2, mt: 2, fontWeight: 'bold' }}>
          {children}
        </Typography>
      ),
      h2: ({ children }: any) => (
        <Typography variant="h5" component="h2" sx={{ mb: 1.5, mt: 2, fontWeight: 'bold' }}>
          {children}
        </Typography>
      ),
      h3: ({ children }: any) => (
        <Typography variant="h6" component="h3" sx={{ mb: 1, mt: 1.5, fontWeight: 'bold' }}>
          {children}
        </Typography>
      ),
      h4: ({ children }: any) => (
        <Typography variant="subtitle1" component="h4" sx={{ mb: 1, mt: 1.5, fontWeight: 'bold' }}>
          {children}
        </Typography>
      ),
      h5: ({ children }: any) => (
        <Typography variant="subtitle2" component="h5" sx={{ mb: 0.5, mt: 1, fontWeight: 'bold' }}>
          {children}
        </Typography>
      ),
      h6: ({ children }: any) => (
        <Typography variant="body1" component="h6" sx={{ mb: 0.5, mt: 1, fontWeight: 'bold' }}>
          {children}
        </Typography>
      ),
      // Paragraphs
      p: ({ children }: any) => (
        <Typography variant="body2" sx={{ mb: 1.5, lineHeight: 1.6 }}>
          {children}
        </Typography>
      ),
      // Lists
      ul: ({ children }: any) => (
        <Box component="ul" sx={{ mb: 1.5, pl: 2, '& li': { mb: 0.5 } }}>
          {children}
        </Box>
      ),
      ol: ({ children }: any) => (
        <Box component="ol" sx={{ mb: 1.5, pl: 2, '& li': { mb: 0.5 } }}>
          {children}
        </Box>
      ),
      li: ({ children }: any) => (
        <Typography component="li" variant="body2" sx={{ lineHeight: 1.6 }}>
          {children}
        </Typography>
      ),
      // Code blocks
      code: ({ children, className, ...props }: any) => {
        const inline = !className;
        if (inline) {
          return (
            <Box
              component="code"
              sx={{
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                padding: '2px 4px',
                borderRadius: '4px',
                fontSize: '0.875em',
                fontFamily: 'monospace',
                color: '#d63384',
              }}
            >
              {children}
            </Box>
          );
        }
        return (
          <Paper
            sx={{
              backgroundColor: '#f8f9fa',
              border: '1px solid #e9ecef',
              borderRadius: 1,
              p: 2,
              mb: 2,
              overflow: 'auto',
            }}
          >
            <Box
              component="pre"
              sx={{
                margin: 0,
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                lineHeight: 1.5,
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
              }}
            >
              <code className={className}>{children}</code>
            </Box>
          </Paper>
        );
      },
      // Blockquotes
      blockquote: ({ children }: any) => (
        <Paper
          sx={{
            borderLeft: '4px solid #9C6FE4',
            backgroundColor: 'rgba(156, 111, 228, 0.05)',
            p: 2,
            mb: 2,
            fontStyle: 'italic',
          }}
        >
          {children}
        </Paper>
      ),
      // Links
      a: ({ href, children }: any) => (
        <Typography
          component="a"
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          sx={{
            color: '#9C6FE4',
            textDecoration: 'none',
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          {children}
        </Typography>
      ),
      // Tables
      table: ({ children }: any) => (
        <Paper sx={{ mb: 2, overflow: 'auto' }}>
          <Box component="table" sx={{ width: '100%', borderCollapse: 'collapse' }}>
            {children}
          </Box>
        </Paper>
      ),
      th: ({ children }: any) => (
        <Box
          component="th"
          sx={{
            border: '1px solid #dee2e6',
            backgroundColor: '#f8f9fa',
            p: 1,
            textAlign: 'left',
            fontWeight: 'bold',
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            {children}
          </Typography>
        </Box>
      ),
      td: ({ children }: any) => (
        <Box component="td" sx={{ border: '1px solid #dee2e6', p: 1 }}>
          <Typography variant="body2">{children}</Typography>
        </Box>
      ),
      // Horizontal rule
      hr: () => <Divider sx={{ my: 2 }} />,
      // Strong/Bold
      strong: ({ children }: any) => (
        <Typography component="strong" sx={{ fontWeight: 'bold' }}>
          {children}
        </Typography>
      ),
      // Emphasis/Italic
      em: ({ children }: any) => (
        <Typography component="em" sx={{ fontStyle: 'italic' }}>
          {children}
        </Typography>
      ),
    }}
  >
    {content}
  </ReactMarkdown>
);
