import { Box, Divider, Typography } from '@mui/material';
import { AppButton } from 'src/components/common/app-button';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table';
import { AppContainer } from 'src/components/common';

import useScheduledTasksTiew from './use-scheduled-tasks-view';

const ScheduledTasksView = () => {
  const {
    headLabels,
    tasksData,
    isLoading,
    columns,
    table,
    openDeleteDialog,
    setOpenDeleteDialog,
    handleConfirmDelete,
    isDeleting,
    openToggleDialog,
    setOpenToggleDialog,
    selectedTask,
    handleConfirmToggle,
    isToggling,
  } = useScheduledTasksTiew();

  return (
    <AppContainer>
      <Typography variant="h3" fontWeight={800} color="text.primary">
        Scheduled Tasks
      </Typography>
      <Typography sx={{ color: 'rgba(15, 14, 17, 0.65)' }}>
        you can check your scheduled tasks and show the results from this table
      </Typography>
      <Divider sx={{ my: '24px' }} />
      <AppTable
        headLabels={headLabels}
        dataCount={tasksData?.total || 0}
        isLoading={isLoading}
        data={tasksData?.tasksScheduled || []}
        columns={columns}
        table={table}
        noDataLabel="No scheduled tasks found"
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        title={
          <Typography variant="h4" textAlign="center">
            Delete Scheduled Task
          </Typography>
        }
        content="Are you sure you want to delete this scheduled task? This action cannot be undone."
        icon={
          <Iconify
            icon="material-symbols:warning"
            sx={{
              mx: 'auto',
              width: '70px',
              height: '70px',
              color: 'error.main',
            }}
          />
        }
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton
              label="Cancel"
              variant="outlined"
              onClick={() => setOpenDeleteDialog(false)}
              color="inherit"
              size="large"
            />
            <AppButton
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
              isLoading={isDeleting}
              size="large"
            />
          </Box>
        }
      />

      {/* Toggle Status Confirmation Dialog */}
      <ConfirmDialog
        open={openToggleDialog}
        onClose={() => setOpenToggleDialog(false)}
        title={
          <Typography variant="h4" textAlign="center">
            {selectedTask?.isActive ? 'Pause' : 'Resume'} Scheduled Task
          </Typography>
        }
        content={
          selectedTask?.isActive
            ? 'This scheduled task is currently active and running. Do you want to pause it?'
            : 'This scheduled task is currently paused. Do you want to resume it?'
        }
        icon={
          <Iconify
            icon={selectedTask?.isActive ? 'eva:pause-circle-fill' : 'eva:play-circle-fill'}
            sx={{
              mx: 'auto',
              width: '70px',
              height: '70px',
              color: selectedTask?.isActive ? 'warning.main' : 'success.main',
            }}
          />
        }
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton
              label="Cancel"
              variant="outlined"
              onClick={() => setOpenToggleDialog(false)}
              color="inherit"
              size="large"
            />
            <AppButton
              label={selectedTask?.isActive ? 'Pause' : 'Resume'}
              variant="contained"
              color={selectedTask?.isActive ? 'warning' : 'success'}
              onClick={handleConfirmToggle}
              isLoading={isToggling}
              size="large"
            />
          </Box>
        }
      />
    </AppContainer>
  );
};

export default ScheduledTasksView;
