import { useAuthContext } from './use-auth-context';

// ----------------------------------------------------------------------

export type PermissionCheckOptions = {
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
};

export function usePermissions() {
  const { user } = useAuthContext();

  const userPermissions = user?.permissions || [];

  // Check if user has specific permission(s)
  const hasPermission = (
    permission: string | string[],
    options: PermissionCheckOptions = { requireAll: false }
  ): boolean => {
    if (!user || !userPermissions.length) {
      return false;
    }

    if (typeof permission === 'string') {
      return userPermissions.includes(permission);
    }

    if (Array.isArray(permission)) {
      if (options.requireAll) {
        // User must have ALL permissions
        return permission.every((perm) => userPermissions.includes(perm));
      }
      // User needs ANY of the permissions
      return permission.some((perm) => userPermissions.includes(perm));
    }

    return false;
  };

  // Check if user has permission for specific actions
  const canView = (resource: string): boolean => {
    return hasPermission(`get-${resource}`) || hasPermission(`get-${resource}s`);
  };

  const canCreate = (resource: string): boolean => {
    return hasPermission(`create-${resource}`);
  };

  const canUpdate = (resource: string): boolean => {
    return hasPermission(`update-${resource}`);
  };

  const canDelete = (resource: string): boolean => {
    return hasPermission(`delete-${resource}`);
  };

  // Check if user can access specific pages/features
  const canAccessAgents = (): boolean => {
    return hasPermission(['get-agents', 'get-agent']);
  };

  const canAccessTemplates = (): boolean => {
    return hasPermission(['get-templates', 'get-template']);
  };

  const canAccessTeams = (): boolean => {
    return hasPermission(['get-templates-teams', 'get-templates-team']);
  };

  const canAccessAdminTemplates = (): boolean => {
    return hasPermission(['get-admin-templates', 'get-admin-template']);
  };

  const canAccessTools = (): boolean => {
    return hasPermission(['get-tools', 'get-tool']);
  };

  const canAccessUsers = (): boolean => {
    return hasPermission('get-users');
  };

  const canAccessRoles = (): boolean => {
    return hasPermission(['get-roles', 'get-role']);
  };

  const canAccessTasks = (): boolean => {
    return hasPermission('get-tasks');
  };

  const canAccessChats = (): boolean => {
    return hasPermission(['get-chats', 'get-chat']);
  };

  const canManageCategories = (): boolean => {
    return hasPermission([
      'get-categories',
      'create-category',
      'update-category',
      'delete-category',
    ]);
  };

  // Check if user is admin
  const isAdmin = (): boolean => {
    return hasPermission('login-admin');
  };

  // Check if user can toggle developer mode
  const canToggleDeveloperMode = (): boolean => {
    return hasPermission('toggle-developer-mode');
  };

  return {
    userPermissions,
    hasPermission,
    canView,
    canCreate,
    canUpdate,
    canDelete,
    canAccessAgents,
    canAccessTemplates,
    canAccessTeams,
    canAccessAdminTemplates,
    canAccessTools,
    canAccessUsers,
    canAccessRoles,
    canAccessTasks,
    canAccessChats,
    canManageCategories,
    isAdmin,
    canToggleDeveloperMode,
  };
}
