import { Ava<PERSON>, Box, Chip, Typography } from '@mui/material';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Iconify } from 'src/components/iconify';
import LongMenu from 'src/components/long-menu';
import { AppTablePropsType } from 'src/components/table/app-table/types/app-table';
import { paths } from 'src/routes/paths';
import { Template, useTemplatesApi } from 'src/services/api/use-templates-api';
import { fDate } from 'src/utils/format-time';
import { preview } from 'vite';

export const usePrivateTemplatesTable = (privateTemplates: Template[]) => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<Template | null>(null);
  const [openPublishDialog, setOpenPublishDialog] = useState(false);
  const [templateId, setTemplateId] = useState('');
  const { useDeleteTemplate, usePublishTemplate } = useTemplatesApi();
  const { mutate: deleteTemplate, isPending } = useDeleteTemplate();
  const { mutate: publishTemplate, isPending: isPendingPublish } = usePublishTemplate(templateId);
  const navigate = useNavigate();

  const handlePublishTemplate = () => {
    publishTemplate(
      {},
      {
        onSuccess: () => {
          setOpenPublishDialog(false);
        },
      }
    );
  };

  const handleDeleteTemplate = (id: number | string) => {
    if (id)
      deleteTemplate(id, {
        onSuccess: () => {
          handleCloseConfirmDialog();
        },
      });
  };

  const handleOpenConfirmDialog = (template: Template) => {
    setTemplateToDelete(template);
    setOpenConfirmDialog(true);
  };

  const handleCloseConfirmDialog = () => {
    setOpenConfirmDialog(false);
    setTemplateToDelete(null);
  };

  const handleConfirmDelete = () => {
    if (templateToDelete?.id) {
      handleDeleteTemplate(templateToDelete.id);
    }
  };

  const getMenuOptions = (template: Template) => [
    {
      label: 'edit',
      icon: 'eva:edit-fill',
      onClick: () => {
        // TODO: implement edit navigation/handler
        navigate(paths.dashboard.agents.edit.replace(':id', template.id.toString()));
      },
      color: 'inherit',
    },
    {
      label: 'publish',
      icon: 'material-symbols:publish',
      onClick: () => {
        // TODO: implement edit navigation/handler
        setTemplateId(template?.id?.toString());
        setOpenPublishDialog(true);
      },
      color: 'inherit',
    },
    {
      label: 'clone',
      icon: 'material-symbols-light:cyclone',
      onClick: () =>
        navigate(paths.dashboard.agents.clone(template?.id), {
          state: template?.name,
        }),
      color: 'primary.main',
    },
    {
      label: 'delete',
      icon: 'eva:trash-2-outline',
      onClick: () => handleOpenConfirmDialog(template),
      color: 'error.main',
    },
  ];

  // Handle file selection
  const handleSelectFile = (id: string) => {
    setSelectedFiles((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleSelectAllFiles = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(privateTemplates.map((template) => template.id.toString()));
    } else {
      setSelectedFiles([]);
    }
  };

  // Table head labels
  const headLabels = [
    { id: 'name', label: 'Name' },
    { id: 'dateCreated', label: 'Date Created', align: 'center' },
    { id: 'type', label: 'Type', align: 'center' },
    { id: 'category', label: 'Category', align: 'center' },
    { id: 'tools', label: 'Tools', align: 'center' },
    { id: 'llmModel', label: 'LLM Model', align: 'center' },
    { id: 'status', label: 'Status', align: 'center' },
    { id: 'publishRequestStatus', label: 'Publish request status', align: 'center' },
    { id: 'action', label: 'Action', align: 'center' },
  ];

  // Table columns configuration
  const columns: AppTablePropsType<Template>['columns'] = [
    {
      name: 'name',
      PreviewComponent: ({ name }) => (
        <Box display="flex" alignItems="center" gap={1.5}>
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: 'text.primary',
              color: 'background.paper',
              fontSize: '0.875rem',
              fontWeight: 600,
            }}
          />
          <Typography variant="body2" fontWeight={500} color="text.primary">
            {name}
          </Typography>
        </Box>
      ),
    },
    {
      name: 'createdAt',
      PreviewComponent: ({ createdAt }) => (
        <Typography variant="body2" color="text.secondary">
          {fDate(createdAt)}
        </Typography>
      ),
    },
    {
      name: 'type',
      PreviewComponent: ({ type }) => (
        <Typography variant="body2" color="text.secondary">
          {type}
        </Typography>
      ),
    },
    {
      name: 'category',
      PreviewComponent: ({ category }) => (
        <Typography variant="body2" color="text.secondary">
          {category.name}
        </Typography>
      ),
    },
    {
      name: 'templateTools',
      PreviewComponent: (data) => (
        <Box display="flex" alignItems="center" justifyContent="center">
          <Iconify icon="mdi:tools" width={16} height={16} style={{ color: '#D32F2F' }} />
        </Box>
      ),
    },
    {
      name: 'model',
      PreviewComponent: ({ model }) => (
        <Typography variant="body2" color="text.secondary">
          {model.replace(/_/g, ' ')}
        </Typography>
      ),
    },
    {
      name: 'status',
      PreviewComponent: (
        { visibility } // Destructure visibility directly from props
      ) => (
        <Chip
          label={visibility}
          size="small"
          sx={{
            bgcolor: visibility === 'PRIVATE' ? '#FFEBEE' : '#E8F5E8',
            color: visibility === 'PRIVATE' ? '#D32F2F' : '#2E7D32',
            fontWeight: 500,
            fontSize: '0.75rem',
            height: '24px',
            '& .MuiChip-label': {
              px: 1.5,
            },
            '&:hover': {
              color: 'white',
            },
          }}
        />
      ),
    },
    {
      name: 'publishRequestStatus',
      PreviewComponent: (template) => (
        <Chip label={template?.publishRequestStatus} variant="soft" />
      ),
    },
    {
      name: 'status',
      cellSx: { width: '50px' },
      PreviewComponent: (template) => <LongMenu options={getMenuOptions(template)} />,
    },
  ];

  // Select configuration
  const selectConfig = {
    idPath: 'id' as keyof Template,
    handleSelectRow: (row: Template) => handleSelectFile(row.id.toString()),
    handleSelectAllRows: (ids: string[]) => (checked: boolean) => handleSelectAllFiles(checked),
    selected: selectedFiles,
    rowCount: privateTemplates.length,
    numSelected: selectedFiles.length,
    selectedRowsActions: [],
  };

  return {
    privateTemplates,
    selectedFiles,
    handleSelectFile,
    handleSelectAllFiles,
    // table,
    columns,
    headLabels,
    selectConfig,
    // Delete dialog
    openConfirmDialog,
    handleCloseConfirmDialog,
    handleConfirmDelete,
    isPending,
    openPublishDialog,
    setOpenPublishDialog,
    handlePublishTemplate,
    isPendingPublish,
  };
};
