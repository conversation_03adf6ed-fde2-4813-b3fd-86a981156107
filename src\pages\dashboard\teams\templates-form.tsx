import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import TeamForm from 'src/sections/teams/form/team-form';
import { useMyTeamTemplatesApi } from 'src/services/api/use-my-teams-api';

const TemplatesForm = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  // Get team type from URL params or location state
  const teamType = (searchParams.get('type') || location.state?.teamType || 'AUTO') as
    | 'AUTO'
    | 'MANUAL';

  const { useGetOneMyTeamTemplate } = useMyTeamTemplatesApi();

  // Fetch team data when editing
  const { data: team } = useGetOneMyTeamTemplate(id!);

  return (
    <>
      <Helmet>
        <title>{id ? 'create team template' : 'edut team template'}</title>
      </Helmet>

      <TeamForm team={team ?? null} initialTeamType={teamType} />
    </>
  );
};

export default TemplatesForm;
