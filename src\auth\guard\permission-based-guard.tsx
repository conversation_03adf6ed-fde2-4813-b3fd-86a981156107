import { m } from 'framer-motion';
import { Container, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { varBounce, MotionContainer } from 'src/components/animate';
import { ForbiddenIllustration } from 'src/assets/illustrations';

import { usePermissions, type PermissionCheckOptions } from '../hooks/use-permissions';

// ----------------------------------------------------------------------

type PermissionBasedGuardProps = {
  children: React.ReactNode;
  permission: string | string[];
  options?: PermissionCheckOptions;
  hasContent?: boolean;
  fallback?: React.ReactNode;
  sx?: object;
};

export function PermissionBasedGuard({
  children,
  permission,
  options = { requireAll: false },
  hasContent = true,
  fallback,
  sx,
}: PermissionBasedGuardProps) {
  const { t } = useTranslation();
  const { hasPermission } = usePermissions();

  const hasAccess = hasPermission(permission, options);

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return hasContent ? (
      <Container component={MotionContainer} sx={{ textAlign: 'center', ...sx }}>
        <m.div variants={varBounce().in}>
          <Typography variant="h3" sx={{ mb: 2 }}>
            {t('error.permissionDenied')}
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <Typography sx={{ color: 'text.secondary' }}>
            {t('error.permissionDeniedDescription')}
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <ForbiddenIllustration sx={{ my: { xs: 5, sm: 10 } }} />
        </m.div>
      </Container>
    ) : null;
  }

  return <>{children}</>;
}

// ----------------------------------------------------------------------

type PermissionWrapperProps = {
  children: React.ReactNode;
  permission: string | string[];
  options?: PermissionCheckOptions;
  fallback?: React.ReactNode;
};

// Simple wrapper that hides content if user doesn't have permission
export function PermissionWrapper({
  children,
  permission,
  options = { requireAll: false },
  fallback = null,
}: PermissionWrapperProps) {
  const { hasPermission } = usePermissions();

  const hasAccess = hasPermission(permission, options);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
