// src/components/hook-form/field-slider.tsx
import { Controller, useFormContext } from 'react-hook-form';
import { Box, Slider, Typography } from '@mui/material';

interface FieldSliderProps {
  name: string;
  label?: string;
  min?: number;
  max: number;
  step?: number;
}

export function RHFFieldSlider({ name, label, min = 1, max, step = 1 }: FieldSliderProps) {
  const { control, watch } = useFormContext();
  const value = watch(name);

  return (
    <Box sx={{ width: '100%', p: 1 }}>
      {label && (
        <Typography variant="body2" gutterBottom>
          {label}: <strong>{value}</strong>
        </Typography>
      )}
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Slider
            {...field}
            value={field.value || min}
            onChange={(_, val) => field.onChange(val)}
            step={step}
            min={min}
            max={max}
            valueLabelDisplay="auto"
            sx={{ color: 'primary.main' }}
          />
        )}
      />
    </Box>
  );
}
