export type Meta = {
  next: number;
};
export interface IGetListServiceParams<ParamsType> {
  url: string;
  params?: ParamsType;
}

export interface IGetItemServiceParams {
  url: string;
  id?: string;
}

export interface IManipulateDataServiceParams {
  url: string;
  withFormData?: boolean;
  withSnackbar?: boolean;
  onSuccess?: (data?: any) => void;
}

export interface IPostServiceParams extends IManipulateDataServiceParams {}

export interface IPutServiceParams extends IManipulateDataServiceParams {
  id?: string;
}
export interface IPatchServiceParams extends IManipulateDataServiceParams {
  id?: string;
  queryKey?: string;
}

export interface IDeleteServiceParams {
  url: string;
  urlAfterSuccess?: string;
  onSuccess?: () => void;
}

export interface Page<T> {
  data: T[];
  status: string;
  meta: Meta;
}

export interface InfiniteScrollResType<T> {
  pages: Page<T>[];
  pageParams: number[];
}
