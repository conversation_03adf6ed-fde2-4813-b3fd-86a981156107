import { Controller, useFormContext } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// v6+: use digital time view for per-minute list
import { renderDigitalClockTimeView } from '@mui/x-date-pickers/timeViewRenderers';

interface DatePickerFieldProps {
  name: string;
  label?: string;
  helperText?: React.ReactNode;
  fullWidth?: boolean;
  required?: boolean;
  disabled?: boolean;
  sx?: object;
  dateAndTime?: boolean; // toggle date vs date+time
}

export function DatePickerField({
  name,
  label,
  helperText,
  fullWidth = true,
  required = false,
  disabled = false,
  sx,
  dateAndTime = false,
  ...other
}: DatePickerFieldProps) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          {dateAndTime ? (
            <DateTimePicker
              {...other} // ensure picker props land on the picker
              label={label}
              value={field.value}
              onChange={(newValue) => field.onChange(newValue)}
              ampm={false}
              views={['year', 'month', 'day', 'hours', 'minutes']}
              // v6+: show all minutes
              timeSteps={{ minutes: 1 }}
              // v5 fallback (ignored by v6)
              minutesStep={1}
              slotProps={{
                textField: {
                  fullWidth,
                  error: !!error,
                  helperText: error ? error.message : helperText,
                  required,
                  disabled,
                  sx,
                },
              }}
              sx={{ height: '32px' }}
            />
          ) : (
            <DatePicker
              {...other}
              label={label}
              value={field.value}
              onChange={(newValue) => field.onChange(newValue)}
              slotProps={{
                textField: {
                  fullWidth,
                  error: !!error,
                  helperText: error ? error.message : helperText,
                  required,
                  disabled,
                  sx,
                },
              }}
              sx={{ height: '32px' }}
            />
          )}
        </LocalizationProvider>
      )}
    />
  );
}
