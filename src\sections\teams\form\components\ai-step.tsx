import { useState } from 'react';
import { Box, Stack, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import FileSearchBar from './file-search-bar';
import AiFileGrid from './ai-file-grid';

// Helper function to get AI model icon
const getAiModelIcon = (type: string) => {
  switch (type) {
    case 'pdf':
      return 'arcticons:deepseek';
    case 'json':
      return 'simple-icons:ollama';
    case 'pptx':
      return 'hugeicons:google-gemini';
    case 'txt':
      return 'simple-icons:openai';
    case 'docx':
      return 'simple-icons:alibabacloud';
    default:
      return 'mdi:robot-outline';
  }
};

// Mock data for AI models
const MOCK_AI_MODELS = [
  {
    name: 'DeepSeek',
    icon: getAiModelIcon('pdf'),
    type: 'pdf',
    size: 1024 * 512, // 512 KB
    modifiedAt: new Date('2023-10-15'),
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    icon: getAiModelIcon('json'),
    type: 'json',
    size: 1024 * 128, // 128 KB
    modifiedAt: new Date('2023-10-20'),
  },
  {
    name: 'Gemini',
    icon: getAiModelIcon('pptx'),
    type: 'pptx',
    size: 1024 * 2048, // 2 MB
    modifiedAt: new Date('2023-10-25'),
  },
  {
    name: 'GPT o1',
    icon: getAiModelIcon('txt'),
    type: 'txt',
    size: 1024 * 5, // 5 KB
    modifiedAt: new Date('2023-11-01'),
  },
  {
    name: 'Qwen',
    icon: getAiModelIcon('docx'),
    type: 'docx',
    size: 1024 * 750, // 750 KB
    modifiedAt: new Date('2023-11-05'),
  },
];

export default function AiStep() {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFile, setSelectedFile] = useState<any | null>(null);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleSelectFile = (file: any) => {
    const isSelected = selectedFile && selectedFile.name === file.name;

    if (isSelected) {
      // Deselect if clicking the same file
      setSelectedFile(null);
    } else {
      // Select only this file
      setSelectedFile(file);
    }
  };

  // Filter AI models based on search query
  const filteredModels = MOCK_AI_MODELS.filter((model) =>
    model.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Stack spacing={2}>
      <Box
        sx={{
          pl: 3,
          maxHeight: '100%',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Typography variant="h4" py={3}>
          {t('components.teams.resources.chooseAiModel')}
        </Typography>
        <FileSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder={t('components.teams.resources.searchAiModels')}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '48px',
              border: '1px solid',
              borderColor: 'divider',
              background: 'background.paper',
            },
          }}
        />
        <Box
          sx={{
            overflow: 'auto',
            flex: 1,
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            msOverflowStyle: 'none',
            scrollbarWidth: 'none',
          }}
        >
          <AiFileGrid
            files={filteredModels}
            onSelectFile={handleSelectFile}
            selectedFiles={selectedFile ? [selectedFile] : []}
          />
        </Box>
      </Box>
    </Stack>
  );
}
